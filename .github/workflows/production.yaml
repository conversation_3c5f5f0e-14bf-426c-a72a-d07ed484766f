name: Build and publish production container

on:
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'Tag for this image build'
        required: true
        default: 'latest'
  workflow_run:
    workflows: ['Bump version']
    types:
      - completed

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      PILLAR: production
    steps:
      - name: Checkout code repository with submodules
        uses: actions/checkout@v3
        with:
          submodules: true # Fetch submodules
          fetch-depth: 0 # Ensure full commit history
          token: ${{ secrets.TOKEN_GITHUB }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: martin-key
          password: ${{ secrets.DOCKER_SECRET }}

      - name: Get version from package.json
        id: package-version
        if: github.event_name == 'workflow_run'
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "IMAGE_TAG=$VERSION" >> $GITHUB_ENV
          echo "Using version from package.json: $VERSION"

      - name: Set image tag from workflow dispatch
        if: github.event_name == 'workflow_dispatch'
        run: |
          echo "IMAGE_TAG=${{ github.event.inputs.image_tag }}" >> $GITHUB_ENV
          echo "Using provided image tag: ${{ github.event.inputs.image_tag }}"

      - name: Build and push with tag
        uses: docker/build-push-action@v4
        with:
          context: '.'
          push: true
          tags: ghcr.io/encorp-io/ryvyl-onboarding:${{ env.IMAGE_TAG }}
          build-args: PROFILE=${{ env.PILLAR }}
