name: Bump version

on:
  push:
    branches:
      - prod
    paths:
      - 'package.json' # Only trigger when package.json changes

jobs:
  tag-version:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.TOKEN_GITHUB }}

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Get version from package.json
        id: package-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "VERSION=v$VERSION" >> $GITHUB_ENV
          echo "Raw version: $VERSION"
          echo "Tag to create: v$VERSION"

      - name: Check if tag exists
        id: check-tag
        run: |
          git fetch --tags
          if git rev-parse "${{ env.VERSION }}" >/dev/null 2>&1; then
            echo "TAG_EXISTS=true" >> $GITHUB_ENV
            echo "Tag ${{ env.VERSION }} already exists"
          else
            echo "TAG_EXISTS=false" >> $GITHUB_ENV
            echo "Tag ${{ env.VERSION }} does not exist yet"
          fi

      - name: Create and push tag
        if: env.TAG_EXISTS == 'false'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag -a ${{ env.VERSION }} -m "Release ${{ env.VERSION }}"
          git push origin ${{ env.VERSION }}
          echo "Created and pushed tag ${{ env.VERSION }}"

      - name: Tag already exists
        if: env.TAG_EXISTS == 'true'
        run: echo "Tag ${{ env.VERSION }} already exists, skipping tag creation"
