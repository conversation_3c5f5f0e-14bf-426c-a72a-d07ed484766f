FROM node:20

# MongoDB
ENV MONGODB_HOST=
ENV MONGODB_PORT=
ENV MONGODB_DATABASE_NAME=
ENV MONGODB_USERNAME=
ENV MONGODB_PASSWORD=
ENV MONGODB_URI=

# General
ENV PORT=
ENV JWT_SECRET_KEY=
ENV DEPLOYMENT=

# ACI
ENV ACI_API_URL=
ENV ACI_API_KEY=
ENV ACI_PSP_ID=
ENV ACI_PSP_NAME=
ENV ACI_CLEARING_INSTITUTE=

# Omnipay
ENV OMNIPAY_INSTITUTION_NUMBER=
ENV OMNIPAY_BOARDING_SCOPE=
ENV OMNIPAY_ACQUIRER_PROFILE_SCOPE=
ENV OMNIPAY_MAINTENANCE_SCOPE=
ENV OMNIPAY_BASE_URL=
ENV OMNIPAY_APP_KEY=
ENV OMNIPAY_JWS_CERT=
ENV OMNIPAY_JWS_PRIV_KEY=
ENV OMNIPAY_ENVIRONMENT=
ENV OMNIPAY_BOARDING_AUD=
ENV OMNIPAY_ACQUIRER_PROFILE_AUD=
ENV OMNIPAY_MAINTENANCE_AUD=

WORKDIR /ryvyl-integration-layer

COPY . .

RUN npm install
RUN npm run build
RUN npm run prestart

CMD ["npm", "run", "prod"]
