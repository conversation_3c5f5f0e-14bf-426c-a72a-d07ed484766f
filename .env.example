# Mongoose
MONGODB_HOST=<your_mongodb_host>
MONGODB_PORT=<port_that_mongodb_runs>
MONGODB_DATABASE_NAME=<mongodb_database_name>
MONGODB_USERNAME=<mongodb_username>
MONGODB_PASSWORD=<mongodb_user_password>
MONGODB_URI=<mongodb_uri>
CA_CERT=<ca_cert>

# General - these are optional, but good to fill
PORT=<desired_port>
EXTERNAL_PORT=<desired_external_port>
DEPLOYMENT=<DEV/STAGING/PROD>

# JWT
JWT_SECRET_KEY=<your_private_key>
TEST_ACCESS_TOKEN=<your_test_jwt_token> // this is only for dev environment, it is not required in production

# ACI
ACI_API_URL=<aci_api_url>
ACI_API_KEY=<aci_api_key>
ACI_PSP_ID=<aci_psp_id>
ACI_PSP_NAME=<aci_psp_name>
ACI_CLEARING_INSTITUTE=<aci_clearing_institute>

# Omnipay
OMNIPAY_INSTITUTION_NUMBER=<omnipay_institution_number>
OMNIPAY_BOARDING_SCOPE=<omnipay_boarding_scope>
OMNIPAY_ACQUIRER_PROFILE_SCOPE=<omnipay_acquirer_profile_scope>
OMNIPAY_MAINTENANCE_SCOPE=<omnipay_maintenance_scope>
OMNIPAY_BASE_URL=<omnipay_base_url>
OMNIPAY_APP_KEY=<omnipay_app_key>
OMNIPAY_JWS_CERT=<omnipay_jws_cert>
OMNIPAY_JWS_PRIV_KEY=<omnipay_jws_priv_key>
OMNIPAY_ENVIRONMENT=<omnipay_environment>
OMNIPAY_BOARDING_AUD=<omnipay_boarding_aud>
OMNIPAY_ACQUIRER_PROFILE_AUD=<omnipay_acquirer_profile_aud>
OMNIPAY_MAINTENANCE_AUD=<omnipay_maintenance_aud>

# Logger
LOG_LEVEL=<If we want some specific level>
LOGGER_UPLOAD_LOGS=<true or false>
LOGGER_SUBDOMAIN=<Subdomain for the logger>
LOGGER_TOKEN=<Token for the logger>
LOGGER_TAGS=<Tags for the logger separated by comma (,)>

# Kafka
KAFKA_BROKER_URL=<kafka_broker_url>
KAFKA_GROUP_ID=<kafka_group_id>
KAFKA_TOPICS=<kafka_topics_separated_by_comma>

# Cron
START_ACI_ONBOARDING_CRON=<true or false>
START_SCHEDULED_UPDATES_CRON=<true or false>
START_ONBOARDING_MESSAGES_CRON=<true or false>

