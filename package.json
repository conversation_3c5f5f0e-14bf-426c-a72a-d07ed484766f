{"name": "ryvyl-onboarding", "version": "1.2.16", "description": "", "main": "index.js", "scripts": {"preinstall": "node checkNodeVersion", "prestart": "node checkNodeVersion", "start": "ts-node-dev --respawn --transpile-only --exit-child --clear ./src/index.ts", "build": "tsc --sourcemap", "prod": "node ./dist/src/index.js", "test": "jest --verbose --setupFiles dotenv/config", "update-ryvyl-commons": "git submodule update --init --recursive --remote submodules/ryvyl-commons"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"async-mutex": "^0.5.0", "axios": "^1.7.9", "class-validator": "^0.14.1", "common": "file:submodules/ryvyl-commons", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "country-list": "^2.3.0", "crypto": "^1.0.1", "csrf-csrf": "^3.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "i18n-iso-countries": "^7.13.0", "jest": "^29.7.0", "joi": "^17.13.3", "jose": "^5.9.6", "mongoose": "^8.8.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "uuidv4": "^6.2.13", "winston": "^3.17.0", "winston-loggly-bulk": "^3.3.2", "xlsx": "^0.18.5", "xslx": "^1.0.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/country-list": "^2.1.4", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "@types/winston-loggly-bulk": "^3.0.6", "eslint": "^9.17.0", "jsonwebtoken": "^9.0.2", "ts-jest": "^29.3.1", "typescript": "^5.7.2"}}