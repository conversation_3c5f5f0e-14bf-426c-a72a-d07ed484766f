import { model, Schema } from 'mongoose';
import { OnboardingMessage } from '../interfaces/kafka';

const OnboardingMessageSchema = new Schema<OnboardingMessage>({
  acquiringEntityId: { type: String, required: true },
  reference: { type: String, required: true },
  result: { type: Object, required: true },
  processed: { type: Boolean, required: false, default: false }
});

OnboardingMessageSchema.index({ id: 1, reference: 1 }, { unique: true });

const OnboardingMessageModel = model<OnboardingMessage>('OnboardingMessage', OnboardingMessageSchema);

export default OnboardingMessageModel;
