import { Document } from 'mongoose';
import { NewMid } from './omnipay';

export interface MerchantFileData extends Document {
  acquirer_country_code: string;
  acquiring_institution_code: string;
  additional_currencies?: string;
  clearing_institute?: string;
  card_acceptor_city?: string;
  mastercard_payment_facilitator_id?: string;
  mastercard_independent_sales_organization_id?: string;
  sub_merchant_id?: string;
  card_acceptor_country: string;
  card_type: string;
  city: string;
  division_name: string;
  long_terminal_id: string;
  mandatory_3ds: string;
  mastercard_acquirer_bin: string;
  mastercard_requestor_name: string;
  merchant_acceptor_name: string;
  merchant_category_code: string;
  merchant_currency: string;
  merchant_name: string;
  merchant_post_code?: string;
  merchant_state_code?: string;
  merchant_country_code: string;
  merchant_url: string;
  payment_facilitator_name?: string;
  send_dynamic_descriptor?: string;
  street: string;
  sub_merchant_city?: string;
  sub_merchant_country?: string;
  sub_merchant_name?: string;
  sub_merchant_post_code?: string;
  sub_merchant_state_code?: string;
  sub_merchant_street?: string;
  visa_acquirer_bin: string;
  visa_requestor_name: string;
  visa_payment_facilitator_id?: string;
  descriptor_1?: string;
  descriptor_2?: string;
  onboarded: boolean;
  template: string;
  separate_mids?: string;
}

export interface OmnipayMerchantDetails {
  midConfig: NewMid;
  // Required Details
  tradeName: string;
  companyName: string;
  legalForm: string;
  language: string;
  merchantType: 'Traditional' | 'Ecommerce' | 'Both'; // can be 'Traditional', 'Ecommerce' or 'Both'
  residentStatus?: 'Resident' | 'Non-Resident' | 'NA'; // can be 'Resident' 'Non-Resident' or 'NA'
  rcc: string; // will be either description or code
  // Contract
  settlementMethod: 'Daily' | 'Weekly' | 'Weekly (Thurs)' | 'Weekly (Tues)' | 'Monthly'; // can be 'Daily', 'Weekly', 'Weekly (Thurs)', Weekly (Tues) and 'Monthly'
  clientRegion?: string; // can be also 'By Transaction', 'By Transactions Rejects' or 'n/a'
  // Location
  countryCode: string;
  merchantStreet?: string;
  merchantZip?: string;
  merchantCity: string;
  merchantUrls: {
    url: string;
    mcc: string;
  }[];
  // Address
  address: string;
  postCode?: string;
  telephone?: string;
  email?: string;
  // Account
  accountCurrencies: string[];
  accountNumber: string; // For when billingLevel is true
  iban?: string;
  bic?: string;
  bankName?: string;
  bankCity?: string;
  // Optional Details
  registrationNumber?: string;
  vatRegistrationNumber?: string;
  mcOrgId?: string;
}

export interface MerchantApplication extends Document {
  pspIsoReferralAgentName?: string;
  name: string;
  email: string;
  registrationNumber: string;
  taxIdentificationNumber: string;
  country: string;
  city: string;
  zip: string;
  address1: string;
  phone: string[];
  tradeOverInternet: boolean;
  websites: {
    url: string;
    statementDescriptor: string;
    mccClassification: {
      url: string;
      mcc: string;
      certainty: number;
    };
  }[];
  principalRawData: any[];
  tradeName?: string;
  address1TradingAs?: string;
  cityTradingAs?: string;
  countryTradingAs?: string;
  zipTradingAs?: string;
  mandatory3ds: boolean;
  midConfiguration: NewMid;
  omnipayResult: {
    longTerminal: string;
    isMasterMid: boolean;
    childLongTerminals: {
      longTerminal: string;
      accountCurrencies: string[];
    }[];
  };
  correspondenceEmail?: string;
  additionalData: {
    currencies?: string[];
    settlementCurrencies?: string[];
    integrationsRequirements?: {
      integrationType?: string;
      pciComplianceStatus?: string;
      thirdPartyGateway?: string | null;
      integrationOption?: string | null;
      paymentPageHostingPreference?: string;
      paymentPageURL?: string | null;
      mandatory3ds?: boolean;
    };
    targetMarketsAndDistribution?: {
      country?: string;
      volumePercentage?: string;
    };
    bankSettlement?: {
      currency?: string;
      bankCountry?: string;
      bankName?: string;
      bankIban?: string;
      bankCode?: string;
      bankAccountHolder?: string;
    }[];
  };
}
