import { Document } from 'mongoose';
import {
  Contract,
  GlobalFee,
  Hierarchy,
  Location,
  MerchantAccount,
  MerchantAccountFees,
  MerchantAddress,
  MerchantDetail,
  MerchantDevices,
  MerchantProperties,
  MerchantRequest,
  MerchantStatus,
  MerchantToOnboard,
  MerchantReference,
  OptionalDetails,
  Payable,
  PaymentDetails,
  PaymentInstructions,
  Receivable,
  RequiredDetails,
  SalesDetails,
  SchemeDetails,
  Services,
  TransactionCharges
} from '../types/omnipayOnboarding';
import { MerchantLevelCodeEnum } from '../../enums/omnipayEnums';

export interface BaseResponse {
  meta: {
    paging: {
      count: number;
      totalPages: number;
      currentOffset: number;
    };
    messages: {
      code: string;
      title: string;
      detail: string;
    }[];
  };
  errors?: {
    id: string;
    code: string;
    title: string;
    detail: string;
    source: {
      pointer: string;
      parameter: string;
      header: string;
    };
  }[];
}
export interface ReferenceDataResponse extends BaseResponse {
  data: {
    classification: string;
    code: string;
    description: string;
  }[];
}

export interface Reference extends Document {
  code: string;
  referenceIndex: string;
  description: string;
}

export interface MerchantReferenceData extends Document {
  referenceIndex: string;
  referenceDescription: string;
  referenceValues: string[];
}
export interface MerchantReferenceResponse extends BaseResponse {
  data: MerchantReferenceData[];
}

export interface ServiceContractsResponse extends BaseResponse {
  data: ServiceContracts[];
}

export interface ServiceContracts extends Document {
  serviceContractIndex: string;
  serviceContractDescription: string;
}

export interface ServiceContractsSpecificResponse extends BaseResponse {
  data:
    | ServiceContractsTariff[]
    | ServiceContractsAccountSettlement[]
    | ServiceContractsTxCharge[]
    | ServiceContractsAccountFee[]
    | ServiceContractsServiceTariff[]
    | ServiceContractsAddressCategory[];
}

export interface ServiceContractsTariff extends Document {
  serviceContractIndex: string;
  tariffType: string;
  tariffObject: {
    clientTariffIndex: string;
    clientTariffDescription: string;
  };
}

export interface ServiceContractsAccountSettlement extends Document {
  uniqueId: string;
  serviceContractIndex: string;
  settlementMethod: {
    settlementMethodIndex: string;
    settlementMethodDescription: string;
  };
  postingTariff: {
    postingTariffIndex: string;
    postingTariffDescription: string;
  };
  accountType: {
    accountTypeIndex: string;
    accountTypeDescription: string;
  };
  accountCurrency: string;
  settlementChannel: {
    channelIndex: string;
    channelDescription: string;
  };
  settlementTrigger: {
    settlementTriggerIndex: string;
    settlementTriggerDescription: string;
  };
  settlementFrequency: {
    frequencyIndex: string;
    frequencyDescription: string;
  };
  payableTransaction: {
    transactionType: string;
    transactionTypeDescription: string;
  };
  receivableTransaction: {
    transactionType: string;
    transactionTypeDescription: string;
  };
}

export interface ServiceContractsTxCharge extends Document {
  serviceContractIndex: string;
  chargeModel: string;
  recordIdNumber: string;
  merchantTariff: {
    tariffIndex: string;
    tariffDescription: string;
  };
  chargeType: {
    chargeTypeIndex: string;
    chargeTypeIndexDescription: string;
  };
  serviceId: {
    serviceIndex: string;
    serviceDescription: string;
  };
  transactionType: {
    transactionType: string;
    transactionTypeDescription: string;
  };
  captureMethod: {
    captureMethodIndex: string;
    captureMethodDescription: string;
  };
  areaOfEvent: {
    areaOfEventIndex: string;
    areaOfEventDescription: string;
  };
  feeCategory: {
    feeCategoryIndex: string;
    feeCategoryIndexDescription: string;
  };
  effectiveDate: string;
  expiryDate: string;
  feeMode: boolean;
  feePercentage: string;
  feeBase: string;
  feeMinimum: string;
  feeMaximum: string;
  chargeCurrencyCode: string;
  chargeTierLevel: string;
  feeType: {
    feeTypeIndex: string;
    feeTypeDescription: string;
  };
  transactionCurrency: string;
  serviceType: {
    cardServiceTypeIndex: string;
    cardServiceTypeDescription: string;
  };
}

export interface ServiceContractsAccountFee extends Document {
  serviceContractIndex: string;
  feeModel: string;
  recordIdNumber: string;
  clientTariff: {
    clientTariffIndex: string;
    clientTariffDescription: string;
  };
  accountType: {
    accountTypeIndex: string;
    accountTypeDescription: string;
  };
  accountCurrencyCode: string;
  effectiveDate: string;
  expiryDate: string;
  triggerSource: {
    triggerSourceIndex: string;
    triggerSourceDescription: string;
  };
  rule: {
    ruleIDIndex: string;
    ruleIDDescription: string;
  };
  feeBaseCurrencyCode: string;
  feePercentage: string;
  feeBase: string;
  feeMinimum: string;
  feeMaximum: string;
  feeMode: boolean;
  triggerHighValue: string;
  triggerLowValue: string;
  triggerVolumeHigh: string;
  triggerVolumeLow: string;
  feeType: {
    feeTypeIndex: string;
    feeTypeDescription: string;
  };
  rateCategory: {
    rateCategoryIndex: string;
    rateCategoryDescription: string;
  };
}

export interface ServiceContractsServiceTariff extends Document {
  serviceContractIndex: string;
  serviceId: {
    serviceIndex: string;
    serviceDescription: string;
  };
  serviceMerchantTariffs: {
    tariffIndex: string;
    tariffDescription: string;
  };
}

export interface ServiceContractsAddressCategory extends Document {
  serviceContractIndex: string;
  CategoryObject: {
    categoryIndex: string;
    categoryDescription: string;
  };
  required: boolean;
  level: 'MEMBER' | 'SUB_GROUP' | 'GROUP' | 'ALL';
}

export interface OnboardingResponse extends BaseResponse {
  data: {
    applicationNumber: string;
  };
}

export interface OmnipayMerchant extends MerchantToOnboard, Document {
  merchantStatus: string;
}

export interface OmnipayMerchantDetail extends MerchantDetail, Document {}

export interface OmnipayHierarchy extends Hierarchy, Document {}

export interface OmnipayRequiredDetails extends RequiredDetails, Document {}

export interface OmnipayOptionalDetails extends OptionalDetails, Document {}

export interface OmnipayContract extends Contract, Document {}

export interface OmnipayLocation extends Location, Document {}

export interface OmnipaySchemeDetails extends SchemeDetails, Document {}

export interface OmnipayGlobalFee extends GlobalFee, Document {}

export interface OmnipaySalesDetails extends SalesDetails, Document {}

export interface OmnipayMerchantAddress extends MerchantAddress, Document {}

export interface OmnipayPaymentDetails extends PaymentDetails, Document {}

export interface OmnipayPayable extends Payable, Document {}

export interface OmnipayReceivable extends Receivable, Document {}

export interface OmnipayMerchantAccount extends MerchantAccount, Document {}

export interface OmnipayService extends Services, Document {}

export interface OmnipayReference extends MerchantReference, Document {}

export interface OmnipayMerchantDevices extends MerchantDevices, Document {}

export interface OmnipayMerchantAccountFees extends MerchantAccountFees, Document {
  feeModel: 'DEFAULT' | 'INDIVIDUAL';
}

export interface OmnipayTransactionCharges extends TransactionCharges, Document {}

export interface OmnipayMerchantRequest extends MerchantRequest, Document {}

export interface OmnipayPaymentInstructions extends PaymentInstructions, Document {}

export interface OmnipayMerchantProperties extends MerchantProperties, Document {}

export interface ScheduledMerchantUpdate extends Document {
  internalMerchantId: string;
  merchantStatus: MerchantStatus;
  accountFees: MerchantAccountFees[];
  addresses: MerchantAddress[];
  contract: { effectiveDate: string } & Contract;
  txCharges: TransactionCharges[];
  paymentInstructions: PaymentInstructions[];
}

export interface FxRate {
  effectiveDate: string;
  fxCategoryIndex: string;
  baseCurrencyIndex: string;
  baseCurrencyCode: string;
  counterCurrencyIndex: string;
  counterCurrencyCode: string;
  middleRate: string;
  purchaseRate: string;
  salesRate: string;
}

export interface MidEntity {
  externalMerchantId: string;
  internalMerchantId: string;
  parentInternalMerchantId: string;
  merchantStatus: string;
  merchantLevelCode: MerchantLevelCodeEnum;
  contractStatus: string;
}

export interface MidConfiguration {
  id: any;
  entityType: string;
  midToBeAttached: string;
  pspNumber: string;
  isMasterMid: boolean;
}

export interface NewMid {
  longTerminal: string;
  parentLongTerminal: string;
  nextLongTerminal?: string;
  isMasterMid: boolean;
}
