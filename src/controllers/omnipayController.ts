import { getAcquiringProfileCall, maintenanceCall } from '../services/omnipayService';
import { Request, Response } from 'express';
import {
  getMerchantReferencesData,
  getReferenceData,
  getServiceContracts,
  getServiceContractsSpecific
} from '../services/omnipayService';
import { omnipayLogger } from '../configs/logger';
import {
  insertMerchantReferences,
  insertReferences,
  insertServiceContracts,
  merchantUpdates
} from '../services/mongoService';
import {
  BaseResponse,
  ServiceContractsAccountFee,
  ServiceContractsAccountSettlement,
  ServiceContractsAddressCategory,
  ServiceContractsServiceTariff,
  ServiceContractsTariff,
  ServiceContractsTxCharge
} from '../models/interfaces/omnipay';
import {
  insertAccountFees,
  insertAccountSettlements,
  insertAddressCategories,
  insertServicesTariff,
  insertTariffs,
  insertTxCharges
} from '../services/mongoService';
import { MAINTENANCE_CONFIG } from '../utils/constants';

export async function referenceData(req: Request, res: Response) {
  const references = await getReferenceData();

  if (references.length > 0) {
    const referenceInsertResult = await insertReferences(references);

    if (!referenceInsertResult) omnipayLogger.error('Failed to insert references');
  }

  res.status(200).send('Indexes update completed');
}

export async function merchantReferences(req: Request, res: Response) {
  const merchantReferences = await getMerchantReferencesData();

  if (merchantReferences.errors) {
    const errorMessages = merchantReferences.errors.map((err) => `${err.code} ${err.title}`).join(',');
    omnipayLogger.info(errorMessages);
    res.status(500).json({ errors: errorMessages });
    return;
  }

  if (merchantReferences.data.length > 0) {
    const referenceInsertResult = await insertMerchantReferences(merchantReferences.data);

    if (!referenceInsertResult) omnipayLogger.error('Failed to insert references');
  }

  res.status(200).send('Merchant references update completed');
}

export async function acquiringProfileCall(req: Request, res: Response) {
  try {
    const { endpoint } = req.params;
    let dynamicParams = '';
    switch (endpoint) {
      case 'fx-rate':
        const { effectiveDateAfter } = req.query;
        dynamicParams = `effectiveDateAfter=${effectiveDateAfter}&fxCategoryIndex=002`;
        break;
      case 'merchant-hierarchy':
        const { internalMerchantId } = req.query;
        dynamicParams = `parentInternalMerchantId=${internalMerchantId}&extractDirection=DOWN`;
        break;
      default:
        res.status(400).json({ error: 'Unsupported route' });
        return;
    }
    const fxRates = await getAcquiringProfileCall(endpoint, dynamicParams);
    res.status(200).json(fxRates);
  } catch (error: any) {
    res.status(500).json({ error: error });
  }
}

export async function serviceContracts(req: Request, res: Response) {
  try {
    const serviceContracts = await getServiceContracts();

    if (serviceContracts.errors) {
      const errorMessages = serviceContracts.errors.map((err) => `${err.code} ${err.title}`).join(',');
      omnipayLogger.info(errorMessages);
      res.status(500).json({ errors: errorMessages });
      return;
    }
    await insertServiceContracts(serviceContracts.data);

    for (const serviceContract of serviceContracts.data) {
      const tariffs = await getServiceContractsSpecific<ServiceContractsTariff>(
        serviceContract.serviceContractIndex,
        'tariff',
        false
      );

      tariffs.forEach((tariff) => {
        tariff.serviceContractIndex = serviceContract.serviceContractIndex;
      });

      const mongoTariffs = await insertTariffs(tariffs);
      if (!mongoTariffs) omnipayLogger.error('Failed to insert tariffs');

      const accountSettlements = await getServiceContractsSpecific<ServiceContractsAccountSettlement>(
        serviceContract.serviceContractIndex,
        'account-settlement'
      );

      accountSettlements.forEach((accountSettlement) => {
        accountSettlement.uniqueId = formAccSettlementId(serviceContract.serviceContractIndex, accountSettlement);
        accountSettlement.serviceContractIndex = serviceContract.serviceContractIndex;
      });

      const mongoAccountSettlements = await insertAccountSettlements(accountSettlements);
      if (!mongoAccountSettlements) omnipayLogger.error('Failed to insert account settlements');

      const txCharges = await getServiceContractsSpecific<ServiceContractsTxCharge>(
        serviceContract.serviceContractIndex,
        'service-transaction-charges'
      );

      txCharges.forEach((txCharge) => {
        txCharge.serviceContractIndex = serviceContract.serviceContractIndex;
      });

      const mongoTxCharges = await insertTxCharges(txCharges);
      if (!mongoTxCharges) omnipayLogger.error('Failed to insert tx charges');

      const accountFees = await getServiceContractsSpecific<ServiceContractsAccountFee>(
        serviceContract.serviceContractIndex,
        'account-fees'
      );

      accountFees.forEach((accountFee) => {
        accountFee.serviceContractIndex = serviceContract.serviceContractIndex;
      });

      const mongoAccountFees = await insertAccountFees(accountFees);
      if (!mongoAccountFees) omnipayLogger.error('Failed to insert account fees');

      const servicesTariff = await getServiceContractsSpecific<ServiceContractsServiceTariff>(
        serviceContract.serviceContractIndex,
        'services-tariff',
        false
      );

      servicesTariff.forEach((serviceTariff) => {
        serviceTariff.serviceContractIndex = serviceContract.serviceContractIndex;
      });

      const mongoServicesTariff = await insertServicesTariff(servicesTariff);
      if (!mongoServicesTariff) omnipayLogger.error('Failed to insert services tariff');

      const addressCategories = await getServiceContractsSpecific<ServiceContractsAddressCategory>(
        serviceContract.serviceContractIndex,
        'addressCategories',
        false
      );

      addressCategories.forEach((addressCategory) => {
        addressCategory.serviceContractIndex = serviceContract.serviceContractIndex;
      });

      const mongoAddressCategories = await insertAddressCategories(addressCategories);
      if (!mongoAddressCategories) omnipayLogger.error('Failed to insert address categories');

      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
    omnipayLogger.info('Service contracts information update completed');
    res.status(200).send('Service contracts information update completed');
  } catch (error: any) {
    res.status(500).json({ error: error });
  }
}

export async function handleMaintenanceOperation(req: Request, res: Response) {
  const { internalMerchantId, endpoint } = req.params;
  const queryParams = req.query;
  const method = req.method;
  const haveBody = Object.keys(req.body).length > 0;
  const payload = haveBody ? req.body : undefined;

  // Check if the endpoint and method combination exists
  if (!MAINTENANCE_CONFIG[endpoint] || !MAINTENANCE_CONFIG[endpoint][method]) {
    omnipayLogger.error(`Unsupported endpoint or method: ${endpoint} ${method}`);
    res.status(400).json({ error: `Unsupported endpoint or method: ${endpoint} ${method}` });
    return;
  }

  const configDetails = MAINTENANCE_CONFIG[endpoint][method];
  const isActualArray = Array.isArray(payload);

  if (configDetails.payloadRequired) {
    if (!haveBody) {
      omnipayLogger.error(`Missing payload for ${endpoint}`);
      res.status(400).json({ error: `Missing payload for ${endpoint}` });
      return;
    }
    if (configDetails.isPayloadArray) {
      if (!isActualArray) {
        omnipayLogger.error(`Payload for ${endpoint} should be an array`);
        res.status(400).json({ error: `Payload for ${endpoint} should be an array` });
        return;
      }
      for (const item of payload) {
        const { error } = configDetails.schema.validate(item, { abortEarly: false });
        if (error) {
          const errorMessages = error.details.map((err) => err.message.replace(/"/g, ''));
          omnipayLogger.error(`${configDetails.errorMessage}`);
          res.status(400).json({ errors: errorMessages });
          return;
        }
      }
    } else {
      if (isActualArray) {
        omnipayLogger.error(`Payload for ${endpoint} should not be an array`);
        res.status(400).json({ error: `Payload for ${endpoint} should not be an array` });
        return;
      }
      const { error } = configDetails.schema.validate(payload, { abortEarly: false });
      if (error) {
        const errorMessages = error.details.map((err) => err.message.replace(/"/g, ''));
        omnipayLogger.error(`${configDetails.errorMessage}`);
        res.status(400).json({ errors: errorMessages });
        return;
      }
    }
  }

  try {
    const result = await maintenanceCall(method, endpoint, internalMerchantId, queryParams, payload);

    const mongoResult = await merchantUpdates(internalMerchantId, configDetails.part, method, payload);

    if (!mongoResult) omnipayLogger.error(`Failed to update merchant ${internalMerchantId} in MongoDB`);

    res.status(200).json({ message: result });
  } catch (error: any) {
    if (error?.errors) {
      const apiErrors = (error as BaseResponse)?.errors?.map((err) => `${err.code} ${err.title}`)!;
      omnipayLogger.error(apiErrors.join(','));
      res.status(500).json({ error: `${apiErrors.join(',')}` });
    } else {
      omnipayLogger.error(error);
      res.status(500).json({ error: 'Internal server error', details: error ?? 'Unknown error' });
    }
  }
}

function formAccSettlementId(
  serviceContractIndex: string,
  accountSettlement: ServiceContractsAccountSettlement
): string {
  const {
    settlementMethod: { settlementMethodIndex: smIndex },
    postingTariff: { postingTariffIndex: ptIndex },
    accountType: { accountTypeIndex: atIndex },
    accountCurrency
  } = accountSettlement;

  return `${accountCurrency}${serviceContractIndex}${smIndex}${ptIndex}${atIndex}`;
}
