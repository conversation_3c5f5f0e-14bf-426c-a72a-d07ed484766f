import { convertApplicationToAciDetails, convertApplicationToOmnipayDetails } from '../utils/conversion';
import { formPayload, incrementMidNumber } from './omnipayService';
import { MerchantApplication, OmnipayMerchantDetails } from '../models/interfaces/requestDetails';
import { MerchantLevelCodeEnum } from '../enums/omnipayEnums';
import { OmniPayApiError, OmniPayValidationError } from '../utils/errorHandling';
import { publishMessage } from './kafkaService';
import { logger } from '../configs/logger';
import {
  attachMerchantAccount,
  detachMerchantAccount,
  getAttachedMerchantAccounts,
  registerChannel,
  registerDivision,
  registerMerchant,
  registerMerchantAccount,
  updateRiskManagementParams
} from './aciService';
import { clearProcessedFiles, getAwaitingFilesData, markFileAsOnboarded } from './mongoService';
import { MerchantFileData } from '../models/interfaces/requestDetails';
import { Channel, Merchant, MerchantAccount } from '../models/interfaces/aci';
import { Mutex } from 'async-mutex';
import { OnboardingMessage } from '../models/interfaces/kafka';

// Create semaphores for division and merchant creation
const divisionSemaphore = new Mutex();
const merchantSemaphore = new Mutex();

export async function onboardToOmnipay(message: OnboardingMessage): Promise<void> {
  const merchantApplication = message.result as MerchantApplication;
  const omnipayPayload = convertApplicationToOmnipayDetails(merchantApplication);
  const isMasterMid = merchantApplication.midConfiguration.isMasterMid;

  // For Master MID will push its child mids
  const childLongTerminals: {
    longTerminal: string;
    currency: string;
  }[] = [];

  const kafkaMessage = {
    acquiringEntityId: message.acquiringEntityId,
    result: {
      longTerminal: omnipayPayload.midConfig.longTerminal,
      isMasterMid: isMasterMid,
      childLongTerminals: childLongTerminals,
      error: undefined
    }
  };

  try {
    const processEntity = async (
      level: MerchantLevelCodeEnum,
      mcc: string,
      url: string,
      hasBillingLevel: boolean,
      payload: OmnipayMerchantDetails,
      currency?: string
    ) => {
      try {
        const resultMessage = await formPayload(level, mcc, url, hasBillingLevel, payload, currency);
        if (resultMessage === 'Service not responding') throw new Error(resultMessage);
      } catch (error: any) {
        let errorMessage;

        if (error instanceof OmniPayApiError) {
          errorMessage = (error as OmniPayApiError).errors?.map((err) => `${err.code} - ${err.title}`).join('\n') ?? '';
        } else if (error instanceof OmniPayValidationError) {
          errorMessage = (error as OmniPayValidationError).validationErrors.join('\n');
        } else {
          errorMessage = error?.message ?? error;
        }

        logger.error(errorMessage);
        kafkaMessage.result.error = errorMessage.includes('CLIENT_NUMBER_ALREADY_EXISTS') ? undefined : errorMessage;
      }
    };
    if (isMasterMid) {
      const parentPayload = { ...omnipayPayload } as OmnipayMerchantDetails;
      const childPayload = { ...omnipayPayload } as OmnipayMerchantDetails;
      const parentLevel = MerchantLevelCodeEnum.SUB_GROUP;
      const childLevel = MerchantLevelCodeEnum.MEMBER;

      await processEntity(parentLevel, '', '9999', true, parentPayload);

      // Next MIDs are under the master MID number
      childPayload.midConfig.parentLongTerminal = parentPayload.midConfig.longTerminal;
      childPayload.midConfig.longTerminal = parentPayload.midConfig.nextLongTerminal!;

      for (const merchantUrl of omnipayPayload.merchantUrls) {
        const currencies = omnipayPayload.accountCurrencies.filter((c) => c.length === 3);
        for (const currency of currencies) {
          await processEntity(childLevel, merchantUrl.mcc, merchantUrl.url, false, childPayload, currency);

          childPayload.midConfig.longTerminal = incrementMidNumber(childPayload.midConfig.longTerminal);
          childLongTerminals.push({
            longTerminal: childPayload.midConfig.longTerminal,
            currency: currency
          });
        }
      }
    } else {
      await processEntity(
        MerchantLevelCodeEnum.MEMBER,
        omnipayPayload.merchantUrls[0].mcc,
        omnipayPayload.merchantUrls[0].url,
        true,
        omnipayPayload
      );
    }

    kafkaMessage.result.childLongTerminals = childLongTerminals;

    await publishMessage('merchant-application-omnipay-response', 'omnipay-success', kafkaMessage);
  } catch (error: any) {
    let errorMessage;

    if (error instanceof OmniPayApiError) {
      errorMessage = (error as OmniPayApiError).errors?.map((err) => `${err.code} - ${err.title}`).join('\n') ?? '';
    } else if (error instanceof OmniPayValidationError) {
      errorMessage = (error as OmniPayValidationError).validationErrors.join('\n');
    } else {
      errorMessage = error?.message ?? error;
    }

    await publishMessage('merchant-application-omnipay-response', 'omnipay-error', kafkaMessage);
  }
}

export async function onboardToAci(message: OnboardingMessage): Promise<void> {
  const merchantApplication = message.result as MerchantApplication;
  const kafkaMessage = {
    acquiringEntityId: message.acquiringEntityId,
    result: {}
  };

  try {
    const merchantsData = convertApplicationToAciDetails(merchantApplication);
    await processAciMerchants(merchantsData);

    kafkaMessage.result = {
      onboardedEntities: merchantsData.map((merchant) => {
        const settlementCurrency = merchant.merchant_currency;
        const currencies =
          (merchant.additional_currencies?.split(',') ?? []).flat().filter((currency) => currency.length === 3) ?? [];
        currencies.push(settlementCurrency);

        return {
          longTerminal: merchant.long_terminal_id,
          url: merchant.merchant_url,
          currency: currencies.join(',')
        };
      })
    };

    await publishMessage('merchant-application-aci-response', 'aci-success', kafkaMessage);
  } catch (error: any) {
    kafkaMessage.result = {
      error:
        error instanceof OmniPayApiError
          ? (error.errors
              ?.map((err) => `${err.code} - ${err.title} ${err.detail ? `- ${err.detail}` : ''}`)
              ?.join('\n') ?? '')
          : (error?.message ?? error)
    };

    await publishMessage('merchant-application-aci-response', 'aci-error', kafkaMessage);
  }
}

// Below function is for scheduled ACI onboarding from uploaded files
// Will be deprecated in future
export async function executeAciOnboarding(): Promise<void> {
  logger.info('Clearing processed files from database...');
  const result = await clearProcessedFiles();

  if (!result) logger.warn('Failed to clear processed files from database');

  try {
    const merchantsData = await getAwaitingFilesData();

    if (merchantsData.length === 0) {
      logger.info('Cron cycle finished with no merchants to onboard.');
      return;
    }

    await processAciMerchants(merchantsData);

    logger.info('ACI onboarding cron cycle finished.');
  } catch (error) {
    logger.error(`Error in ACI onboarding cron cycle: ${error}`);
  }
}

async function processAciMerchants(merchantsData: MerchantFileData[]) {
  for (const merchant of merchantsData) {
    merchant.long_terminal_id = merchant.long_terminal_id.replace(/[^0-9]/g, '');
    const nameRegex = /[^A-Za-z0-9\s_-]/g;
    const currencyRegex = /[;,|/\s]+/;

    const merchantName = formValidField(merchant.merchant_name, nameRegex);
    const divisionName = formValidField(merchant.division_name, nameRegex);

    // Acquire division lock before creating
    const divisionResult = await divisionSemaphore.runExclusive(async () => {
      return await registerDivision(divisionName.trim());
    });

    // Acquire merchant lock before creating
    const merchantResult = await merchantSemaphore.runExclusive(async () => {
      return await registerMerchant(merchantName.trim(), divisionResult);
    });

    const merchantAccountResults = await registerMerchantAccount(merchant, merchantResult.id);
    if ((merchant.separate_mids?.toUpperCase() ?? 'NO') === 'YES') {
      await channelOperation(merchant, merchantResult, merchantAccountResults, merchantsData, currencyRegex, '_V');
      await channelOperation(merchant, merchantResult, merchantAccountResults, merchantsData, currencyRegex, '_MC');
    } else await channelOperation(merchant, merchantResult, merchantAccountResults, merchantsData, currencyRegex);

    const markResult = await markFileAsOnboarded(merchant);
    if (!markResult) {
      logger.warn(`Failed to mark file ${merchant.long_terminal_id} as onboarded`);
      const message = {
        error: `Failed to mark file ${merchant.long_terminal_id} as onboarded`
      };
      await publishMessage('merchant-application-aci-response', 'aci-error', message);
    }
  }
}

async function channelOperation(
  merchant: MerchantFileData,
  merchantResult: Merchant,
  merchantAccountResults: MerchantAccount[],
  merchantsData: MerchantFileData[],
  currencyRegex: RegExp,
  channelNameSuffix: string = ''
): Promise<void> {
  const channelName = merchant.merchant_url + channelNameSuffix;
  const channelResult = await registerChannel(channelName, merchantResult.id);

  // Pausing in order to prevent parameters to not be updated
  await new Promise((resolve) => setTimeout(resolve, 1000));
  await updateRiskManagementParams('merchants', merchantResult.id, merchant.template, merchant.merchant_category_code);
  await new Promise((resolve) => setTimeout(resolve, 1000));
  await updateRiskManagementParams(
    'channels',
    channelResult.channel,
    merchant.template,
    merchant.merchant_category_code
  );

  for (const merchantAccount of merchantAccountResults) {
    const merchant = merchantsData.find((m) => merchantAccount.name.includes(m.long_terminal_id))!;
    const currencies = merchant.additional_currencies?.split(currencyRegex).map((c) => c.trim()) ?? [];
    currencies.push(merchant.merchant_currency);

    await attachMerchantAccountToChannel(merchant, merchantAccount, channelResult, currencies, channelNameSuffix);
  }
}

async function attachMerchantAccountToChannel(
  merchant: MerchantFileData,
  merchantAccount: MerchantAccount,
  channelResult: Channel,
  currencies: string[],
  channelNameSuffix: string = ''
): Promise<void> {
  try {
    const missingCurrencies = await extractMissingCurrencies(
      merchantAccount,
      channelResult,
      merchant.mandatory_3ds,
      currencies
    );

    if (missingCurrencies.length > 0) {
      logger.warn(
        `${missingCurrencies.length} currencies are not attached with merchant account ${merchantAccount.name}`
      );

      for (const currency of missingCurrencies) {
        if (currency.length !== 3) {
          logger.warn(`Currency ${currency} is not in ISO 4217 format. Skipping...`);
          delete currencies[currencies.indexOf(currency)];
        } else
          await attachMerchantAccount(
            merchantAccount,
            currency.trim(),
            channelResult.channel,
            merchant,
            channelNameSuffix
          );

        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      logger.info(`Attachment cycle finished for channel ${channelResult.channel}`);
      // Wait for 5 seconds to allow ACI to update the attached merchant accounts
      logger.info(`Waiting for ACI to update the attached merchant accounts...`);
      await new Promise((resolve) => setTimeout(resolve, 5000));

      logger.info(`Checking for missing attached merchant accounts...`);

      return await attachMerchantAccountToChannel(
        merchant,
        merchantAccount,
        channelResult,
        currencies,
        channelNameSuffix
      );
    } else {
      logger.info(`Merchant account ${merchantAccount.name} attached for all currencies`);
    }
  } catch (error) {
    logger.error(`Error in attachMerchantAccountToChannel: ${error}`);
    throw error;
  }
}

async function extractMissingCurrencies(
  merchantAccount: MerchantAccount,
  channel: Channel,
  mandatory3ds: string,
  currencies: string[]
): Promise<string[]> {
  let attachedMerchantAccountsResult = await getAttachedMerchantAccounts(channel.channel);
  let attachedMerchantAccounts = attachedMerchantAccountsResult.attachedMerchantAccounts;

  // Detach merchant accounts with empty binCountryLimit
  const accountsToDetach = attachedMerchantAccounts.filter(
    (a) => a.merchantAccountId === merchantAccount.id && !a.binCountryLimit
  );

  if (accountsToDetach.length > 0 && mandatory3ds.toUpperCase() === 'NO') {
    logger.warn(
      `Found ${accountsToDetach.length} accounts with empty binCountryLimit in channel ${channel.channel}. Detaching...`
    );
    try {
      for (const account of accountsToDetach) {
        await detachMerchantAccount(channel.channel, account.id);
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      attachedMerchantAccountsResult = await getAttachedMerchantAccounts(channel.channel);
      attachedMerchantAccounts = attachedMerchantAccountsResult.attachedMerchantAccounts;
    } catch (error) {
      throw error;
    }
  }

  const filteredAttachments = attachedMerchantAccounts
    .filter(
      (a) =>
        a.merchantAccountId === merchantAccount.id && (mandatory3ds.toUpperCase() === 'YES' ? true : a.binCountryLimit)
    )
    .map((a) => a.currency);

  const missingCurrencies = currencies.filter((currency) => !filteredAttachments.includes(currency));

  return missingCurrencies;
}

function formValidField(value: string, regex: RegExp): string {
  return value.replace(regex, '');
}
