import axios, { isAxiosError } from 'axios';
import {
  DivisionInfo,
  MerchantInfo,
  MerchantAccountDetails,
  AttachmentInfo,
  Division,
  Merchant,
  MerchantAccount,
  Channel,
  RiRoKeyInfo,
  AttachedList
} from '../models/interfaces/aci';
import { MerchantFileData } from '../models/interfaces/requestDetails';
import {
  insertMerchantAccount,
  getMerchantAccountsByMerchantId,
  getDivision,
  insertDivision,
  insertMerchant,
  insertChannel,
  getChannel
} from './mongoService';
import { aciLogger } from '../configs/logger';
import {
  standardMerchantAccountForm,
  pfMerchantAccountForm,
  attachedAccountForm,
  channelForm
} from '../utils/formData';
import { Template } from '../enums/template';
import { ACI_CONFIG } from '../configs/config';

const subOrganisation: Record<string, string> = {
  Primary: '************',
  Unicorn: '************',
  '4722': '************',
  '4816': '************',
  '4829': '************',
  '5815': '************',
  '5816': '************',
  '5817': '************',
  '5818': '************',
  '5960': '************',
  '5967': '************',
  '6012': '************',
  '6051': '************',
  '6211': '************',
  '6540': '************',
  '7273': '************',
  '7995': '************',
  Other: '************'
} as const;

const options = {
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
    Accept: 'application/json',
    credentials: ACI_CONFIG.apiKey
  }
};

async function createDivision(formData: FormData): Promise<DivisionInfo> {
  try {
    const response = await axios.post(`${ACI_CONFIG.baseUrl}/psps/${ACI_CONFIG.pspId}/divisions`, formData, options);

    if (response.data.error) throw response.data.error.message;
    return response.data;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw error.response?.data.result.description;
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function getDivisions(): Promise<Division[]> {
  try {
    const response = await axios.get(`${ACI_CONFIG.baseUrl}/psps/${process.env.ACI_PSP_ID}/divisions`, options);

    if (response.data.error) throw response.data.error.message;

    return response.data.divisions;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw error.response?.data.result.description;
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function createMerchant(divisionId: string, formData: FormData): Promise<MerchantInfo> {
  try {
    const response = await axios.post(`${ACI_CONFIG.baseUrl}/divisions/${divisionId}/merchants`, formData, options);

    if (response.data.error) throw response.data.error.message;

    return response.data;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function getMerchants(divisionId: string): Promise<Merchant[]> {
  try {
    const response = await axios.get(`${ACI_CONFIG.baseUrl}/divisions/${divisionId}/merchants`, options);

    if (response.data.error) throw response.data.error.message;

    return response.data.merchants;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function createChannel(merchantId: string, formData: FormData): Promise<Channel> {
  try {
    const response = await axios.post(`${ACI_CONFIG.baseUrl}/merchants/${merchantId}/channels`, formData, options);

    if (response.data.error)
      if (response.data.error.code === '200.300.405') {
        const channels = await getChannels(merchantId);
        return channels.find((c) => c.name === formData.get('name'))!;
      } else {
        throw response.data.error.message;
      }

    return response.data.channelInfo;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function getChannels(merchantId: string): Promise<Channel[]> {
  try {
    const response = await axios.get(`${ACI_CONFIG.baseUrl}/merchants/${merchantId}/channels`, options);

    if (response.data.error) throw response.data.error.message;

    return response.data.channels;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(`Merchant: ${merchantId} - ${error.response?.data}`);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function createMerchantAccount(merchantId: string, formData: FormData): Promise<MerchantAccountDetails> {
  try {
    const response = await axios.post(
      `${ACI_CONFIG.baseUrl}/merchants/${merchantId}/ownedMerchantAccounts`,
      formData,
      options
    );

    if (response.data.error) throw response.data.error.message;

    return response.data;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function getMerchantAccounts(merchantId: string): Promise<MerchantAccount[]> {
  try {
    const response = await axios.get(`${ACI_CONFIG.baseUrl}/merchants/${merchantId}/ownedMerchantAccounts`, options);

    if (response.data.error) throw new Error(response.data.error.message);

    return response.data.merchantAccounts;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(error.response?.data);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function attachMerchantAccountToChannel(channelId: string, formData: FormData): Promise<AttachmentInfo> {
  try {
    const response = await axios.post(
      `${ACI_CONFIG.baseUrl}/channels/${channelId}/attachedMerchantAccounts`,
      formData,
      options
    );

    if (response.data.error) throw response.data.error.message;

    return response.data;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(`Fail attachment for channel ${channelId} - ${error.response?.data}`);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function updateRiskManagementParam(level: string, id: string, formData: FormData): Promise<RiRoKeyInfo> {
  try {
    const response = await axios.post(`${ACI_CONFIG.baseUrl}/${level}/${id}/setting`, formData, options);

    if (response.data.error) throw response.data.error.message;

    aciLogger.info(`RiRo Response:`);
    aciLogger.info(response.data);

    return response.data;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(`Fail update risk management param for ${level} ${id} - ${error.response?.data}`);
      throw new Error(error.response?.data?.result?.description ?? 'Unkown error');
    } else {
      aciLogger.error(error);
      throw error;
    }
  }
}

async function sendMerchantAccountData(merchantId: string, formData: FormData): Promise<MerchantAccount> {
  try {
    const merchantAccountResult = await createMerchantAccount(merchantId, formData);
    const merchantAccount = merchantAccountResult.merchantAccount;
    merchantAccount.associatedMerchantId = merchantId;

    const mongoResult = await insertMerchantAccount(merchantAccount);

    if (!mongoResult)
      throw new Error(`Failed to insert merchant account ${merchantAccountResult.merchantAccount.id} into MongoDB`);

    aciLogger.info(`Merchant account created with name ${formData.get('name')}`);
    return merchantAccountResult.merchantAccount;
  } catch (error) {
    throw error;
  }
}

export async function registerDivision(divisionName: string): Promise<Division> {
  const divisionMongo = await getDivision(divisionName);
  const divisionsAci = await getDivisions();
  const divisionAci = divisionsAci.find((d) => d.name === divisionName);

  if (divisionMongo) {
    aciLogger.info(`Division with name "${divisionName}" already exists. Skipping creation.`);
    return divisionMongo;
  }

  if (divisionAci) {
    aciLogger.info(`Division with name "${divisionName}" already exists in ACI. Skipping creation.`);
    await insertDivision(divisionAci);
    return divisionAci;
  }

  const formData = new FormData();
  formData.append('name', divisionName.trim());
  formData.append('state', 'LIVE');
  formData.append('description', divisionName.trim());
  const divisionResult = await createDivision(formData);

  const mongoResult = await insertDivision(divisionResult.divisionInfo);

  if (!mongoResult) throw new Error(`Failed to insert division ${divisionResult.divisionInfo.id} into MongoDB`);

  aciLogger.info(`Division with name "${divisionName}" created successfully`);
  return divisionResult.divisionInfo;
}

export async function registerMerchant(merchantName: string, division: Division): Promise<Merchant> {
  const merchantsAci = await getMerchants(division.id);
  const merchantAci = merchantsAci.find((m) => m.name === merchantName);

  if (merchantAci) {
    aciLogger.info(
      `Merchant with name "${merchantName}" in division "${division.name}" already exists in ACI. Skipping creation.`
    );
    merchantAci.divisionId = division.id;
    await insertMerchant(merchantAci);
    return merchantAci;
  }

  const formData = new FormData();
  formData.append('name', merchantName);
  formData.append('state', 'LIVE');
  formData.append('description', merchantName);
  const merchantResult = await createMerchant(division.id, formData);

  const mongoResult = await insertMerchant(merchantResult.merchantInfo);

  if (!mongoResult) throw new Error(`Failed to insert merchant ${merchantResult.merchantInfo.id} into MongoDB`);

  aciLogger.info(`Merchant created for division ${division.name}`);
  return merchantResult.merchantInfo;
}

export async function registerMerchantAccount(
  fileData: MerchantFileData,
  merchantId: string
): Promise<MerchantAccount[]> {
  let formData = new FormData();
  const accountForm =
    fileData.template === Template.PaymentFacilitator ? pfMerchantAccountForm : standardMerchantAccountForm;
  const merchantAccountResults: MerchantAccount[] = [];
  try {
    const supports3DSecureValue = fileData.mandatory_3ds;
    const cardTypes = fileData.card_type.split(',');
    const cardType = cardTypes.length > 1 ? 'VMC' : cardTypes.includes('VISA') ? 'V' : 'MC';
    const name =
      fileData.template === Template.PaymentFacilitator
        ? `${fileData.merchant_url}_${cardType}_${fileData.merchant_category_code}_${fileData.merchant_currency}_${fileData.long_terminal_id}_3DS${supports3DSecureValue}`
        : `${fileData.merchant_url}_${cardType}_${fileData.merchant_category_code}_${fileData.long_terminal_id}_3DS${supports3DSecureValue}`;

    if (fileData.mandatory_3ds.toUpperCase() === 'YES') {
      const existingAccount = await merchantAccountExist(name, merchantId);
      if (existingAccount) merchantAccountResults.push(existingAccount);
      else {
        formData = accountForm(name, true, fileData);
        const merchantAccountResult = await sendMerchantAccountData(merchantId, formData);
        merchantAccountResults.push(merchantAccountResult);
      }
    } else {
      // Optional 3DS
      // 2 merchant accounts one with 3DS and one without
      const existingAccountNo = await merchantAccountExist(name, merchantId);
      if (existingAccountNo) merchantAccountResults.push(existingAccountNo);
      else {
        formData = accountForm(name, false, fileData);
        const merchantAccountResult1 = await sendMerchantAccountData(merchantId, formData);
        merchantAccountResults.push(merchantAccountResult1);
      }

      const existingAccountYes = await merchantAccountExist(name.replace('_3DSNO', '_3DSYES'), merchantId);
      if (existingAccountYes) merchantAccountResults.push(existingAccountYes);
      else {
        formData = accountForm(name.replace('_3DSNO', '_3DSYES'), true, fileData);
        const merchantAccountResult2 = await sendMerchantAccountData(merchantId, formData);
        merchantAccountResults.push(merchantAccountResult2);
      }
    }
    return merchantAccountResults;
  } catch (error) {
    throw error;
  }
}

export async function merchantAccountExist(name: string, merchantId: string): Promise<MerchantAccount | undefined> {
  const merchantAccountsMongo = await getMerchantAccountsByMerchantId(merchantId);
  const merchantAccountMongo = merchantAccountsMongo.find((m) => m.name === name);
  const merchantAccountsAci = await getMerchantAccounts(merchantId);
  const merchantAccountAci = merchantAccountsAci.find((m) => m.name === name);

  if (merchantAccountMongo) {
    aciLogger.info(`Merchant accounts already exist for "${name}". Skipping creation.`);
    return merchantAccountMongo;
  }

  if (merchantAccountAci) {
    aciLogger.info(`Merchant accounts already exist for "${name}" in ACI. Skipping creation.`);
    merchantAccountAci.associatedMerchantId = merchantId;
    insertMerchantAccount(merchantAccountAci);
    return merchantAccountAci;
  }

  return undefined;
}

export async function registerChannel(merchantUrl: string, merchantId: string): Promise<Channel> {
  const channelName = merchantUrl
    .replace(/\./g, '_')
    .replace(/\b(https?|www)\b/g, '')
    .replace(/[^A-Za-z0-9_-\s]/g, '');
  const channelMongo = await getChannel(merchantId, channelName);
  const channelsAci = await getChannels(merchantId);
  const channelAci = channelsAci.find((c) => c.name === channelName);

  if (channelAci) {
    aciLogger.info(`Channel already exist for "${channelName}" in ACI. Skipping creation.`);
    await insertChannel(channelAci);
    return channelAci;
  }

  const formData = channelForm(channelName);
  const channelResult = await createChannel(merchantId, formData);
  const mongoResult = await insertChannel(channelResult);

  if (!mongoResult) throw new Error(`Failed to insert channel ${channelName} into MongoDB`);

  aciLogger.info(`Channel created for merchant ${merchantId}`);
  return channelResult;
}

export async function attachMerchantAccount(
  merchantAccount: MerchantAccount,
  currency: string,
  channelId: string,
  jsonFile: MerchantFileData,
  channelNameSuffix: string = ''
): Promise<void> {
  const formData = attachedAccountForm(currency, merchantAccount, jsonFile, channelNameSuffix);
  const result = await attachMerchantAccountToChannel(channelId, formData);

  try {
    if (result.attachedMerchantAccount)
      aciLogger.info(
        `Merchant account ${merchantAccount.name} attached to channel ${channelId} for ${currency} currency`
      );
    else
      aciLogger.warn(
        `Unable to attach account ${merchantAccount.name} to channel ${channelId} for ${currency} currency`
      );
  } catch (error) {
    throw error;
  }
}

export async function getAttachedMerchantAccounts(channelId: string): Promise<AttachedList> {
  try {
    const response = await axios.get(`${ACI_CONFIG.baseUrl}/channels/${channelId}/attachedMerchantAccounts`, options);

    if (response.data.error) throw response.data.error.message;

    return response.data as AttachedList;
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(`Channel: ${channelId} - ${error.response?.data}`);
      throw error.response?.data.result.description;
    } else {
      aciLogger.error(`Channel: ${channelId} - ${error}`);
      throw error;
    }
  }
}

export async function updateRiskManagementParams(
  level: string,
  id: string,
  template: string,
  mcc: string
): Promise<void> {
  const keys = [
    '*/type:entity/module:ctpe/processing:risk/risk:retaildecisionsdirect/retaildecisionsdirect:ebtName',
    '*/type:entity/module:ctpe/processing:risk/risk:retaildecisionsdirect/retaildecisionsdirect:merchantCategoryCode'
  ];

  try {
    const ebtName =
      template === Template.PaymentFacilitator
        ? subOrganisation['Unicorn']
        : Object.keys(subOrganisation).includes(mcc)
          ? subOrganisation[mcc]
          : subOrganisation['Other'];

    aciLogger.info(`EBT Name: ${ebtName}, MCC: ${mcc}`);
    for (const key of keys) {
      const formData = new FormData();
      formData.append('key', key);
      if (key.includes(':ebtName')) formData.append('value', ebtName);
      else formData.append('value', mcc);

      try {
        const result = await updateRiskManagementParam(level, id, formData);
        aciLogger.info(`Risk management parameter updated for channel ${id} - ${result.key}, ${result.value}`);
      } catch (error) {
        aciLogger.error(`Fail update risk management param for ${level} ${id} - ${error}`);
      }
    }
    aciLogger.info(`Risk management parameters updated for channel ${id}`);
  } catch (error) {
    throw error;
  }
}

export async function detachMerchantAccount(channelId: string, attachmentId: string): Promise<void> {
  try {
    const response = await axios.delete(`${ACI_CONFIG.baseUrl}/attachedMerchantAccounts/${attachmentId}`, options);

    if (response.data.error) throw response.data.error.message;

    aciLogger.info(`Successfully detached merchant account attachment ${attachmentId} from channel ${channelId}`);
  } catch (error: any) {
    if (isAxiosError(error)) {
      aciLogger.error(
        `Fail detachment for Channel: ${channelId}, Attachment: ${attachmentId}, ${error.response?.data}`
      );
      throw error.response?.data.result.description;
    } else {
      aciLogger.error(`Fail detachment for Channel: ${channelId}, Attachment: ${attachmentId}, ${error}`);
      throw error;
    }
  }
}
