import { logger } from '../configs/logger';
import {
  checkScheduledUpdates,
  clearOnboardingMessages,
  dropScheduledUpdates,
  getOnboardingMessages,
  getScheduledUpdates,
  markOnboardingMessageAsProcessed
} from './mongoService';
import cron from 'node-cron';
import { executeAciOnboarding, onboardToAci, onboardToOmnipay } from './onboardingService';
import { midOperation } from './midService';

let aciOnboardingStarted = false;
let aciOnboardingActive = false;

export async function startAciOnboardingCronJob(): Promise<void> {
  if (!aciOnboardingStarted) {
    aciOnboardingStarted = true;
    logger.info('Onboarding cron initiated.');

    // Scheduled to run every minute
    cron.schedule('* * * * *', async () => {
      if (aciOnboardingActive) {
        logger.info('Cron is active. Skipping cycle.');
        return;
      }
      aciOnboardingActive = true;
      await executeAciOnboarding();
      aciOnboardingActive = false;
    });
  }
}

let scheduledUpdatesCronStarted = false;
let scheduledUpdatesCronActive = false;

export async function startCheckingScheduledUpdate(): Promise<void> {
  if (!scheduledUpdatesCronStarted) {
    scheduledUpdatesCronStarted = true;
    logger.info('Scheduled Updates cron initiated.');

    cron.schedule('* * * * *', async () => {
      if (scheduledUpdatesCronActive) {
        logger.info('Scheduled Updates cron is active. Skipping cycle.');
        return;
      }

      scheduledUpdatesCronActive = true;
      await startCheckingMerchantUpdate();
      scheduledUpdatesCronActive = false;
    });
  }
}

let onboardingMessagesCronStarted = false;
let onboardingMessagesCronActive = false;

export async function startOnboardingMessagesCron(): Promise<void> {
  if (!onboardingMessagesCronStarted) {
    onboardingMessagesCronStarted = true;
    logger.info('Onboarding Messages cron initiated.');

    cron.schedule('* * * * *', async () => {
      if (onboardingMessagesCronActive) {
        logger.info('Onboarding Messages cron is active. Skipping cycle.');
        return;
      }

      onboardingMessagesCronActive = true;
      await startCheckingOnboardingMessages();
      onboardingMessagesCronActive = false;
    });
  }
}

async function startCheckingMerchantUpdate(): Promise<void> {
  try {
    const scheduledUpdates = await getScheduledUpdates();

    await Promise.all(
      scheduledUpdates.map(async (update) => {
        try {
          const result = await checkScheduledUpdates(update);

          if (!result) throw new Error(`Failed to scan merchant updates for ${update.internalMerchantId}`);
        } catch (error: any) {
          throw error.message;
        }
      })
    );

    await Promise.all(
      scheduledUpdates.map(async (update) => {
        try {
          const result = await dropScheduledUpdates(update.internalMerchantId);

          if (!result) throw new Error(`Failed to drop merchant updates for ${update.internalMerchantId}`);
        } catch (error: any) {
          throw error.message;
        }
      })
    );

    logger.info('Scheduled updates cron cycle finished.');
  } catch (error: any) {
    logger.error(`Error in scheduled updates cron cycle: ${error}`);
  }
}

async function startCheckingOnboardingMessages(): Promise<void> {
  try {
    const clearResult = await clearOnboardingMessages();
    if (!clearResult) logger.warn('Failed to clear processed onboarding messages from database');

    const onboardingMessages = await getOnboardingMessages();

    for (const message of onboardingMessages) {
      try {
        switch (message.reference) {
          case 'merchant-application-omnipay':
            await onboardToOmnipay(message);
            break;
          case 'merchant-application-mid':
            await midOperation(message);
            break;
          case 'merchant-application-aci':
            await onboardToAci(message);
            break;
          default:
            logger.warn(`${message.reference} not implemented`);
            break;
        }
        await markOnboardingMessageAsProcessed(message.acquiringEntityId, message.reference);
      } catch (error: any) {
        logger.error(`Error in onboarding message cron cycle: ${error}`);
      }
    }
  } catch (error: any) {
    logger.error(`Error in onboarding message cron cycle: ${error}`);
  }

  logger.info('Onboarding messages cron cycle finished.');
}
