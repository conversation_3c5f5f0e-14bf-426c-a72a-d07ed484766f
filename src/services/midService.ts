import { logger } from '../configs/logger';
import { MidConfiguration, MidEntity, NewMid } from '../models/interfaces/omnipay';
import { getAcquiringProfileCall } from './omnipayService';
import { OmniPayApiError } from '../utils/errorHandling';
import { publishMessage } from './kafkaService';
import { OnboardingMessage } from '../models/interfaces/kafka';

export async function midOperation(message: OnboardingMessage): Promise<void> {
  const midConfiguration = message.result as MidConfiguration;

  const responseMessage = {
    acquiringEntityId: message.acquiringEntityId,
    result: {}
  };

  try {
    const pspReferenceCorrect = await isPspReferenceCorrect(
      midConfiguration.midToBeAttached,
      midConfiguration.pspNumber
    );
    if (!pspReferenceCorrect) {
      logger.error(`PSP reference is not correct.`);
      responseMessage.result = {
        error: `PSP reference is not correct.`
      };
      await publishMessage('merchant-application-mid-response', 'mid-error', responseMessage);
      return;
    }

    const midHierarchy = (await getAcquiringProfileCall(
      'merchant-hierarchy',
      `parentInternalMerchantId=${midConfiguration.midToBeAttached}&extractDirection=DOWN`
    )) as MidEntity[];

    if (midConfiguration.midToBeAttached === '20100000')
      await formPspMid(message.acquiringEntityId, midHierarchy, midConfiguration);
    else await formMid(message.acquiringEntityId, midHierarchy, midConfiguration);
  } catch (error: any) {
    let errorMessage = '';
    if (error instanceof OmniPayApiError) {
      errorMessage = error.errors?.map((err) => `${err.code} - ${err.title}`)?.join('\n') ?? '';
      logger.error(errorMessage);
    } else {
      errorMessage = error;
      logger.error(error);
    }

    responseMessage.result = {
      error: errorMessage
    };

    await publishMessage('merchant-application-mid-response', 'mid-error', responseMessage);
  }
}

async function formMid(id: string, midHierarchy: MidEntity[], midConfiguration: MidConfiguration): Promise<void> {
  const responseMessage = {
    acquiringEntityId: id,
    result: {}
  };
  const isMasterMid = midConfiguration.isMasterMid;
  const mastedMidFlag = isMasterMid ? '9' : '0';
  let newMid = '';
  let newLongTerminalId = '';

  const parentTerminalId = midHierarchy.find(
    (mid) => mid.internalMerchantId === midConfiguration.midToBeAttached
  )?.externalMerchantId;

  // Get only active mids and by according PSP number
  const filteredMids = midHierarchy.filter(
    (mid) =>
      mid.merchantStatus.toUpperCase() !== 'CLOSED' &&
      mid.internalMerchantId.substring(1, 4) === midConfiguration.pspNumber
  );

  const masterMids = filteredMids.filter((mid) => mid.internalMerchantId.at(4) === '9');
  const normalMids = filteredMids.filter((mid) => mid.internalMerchantId.at(4) === '0');
  const sortedMids = (isMasterMid ? masterMids : normalMids).sort(
    (a, b) => Number(b.internalMerchantId) - Number(a.internalMerchantId)
  );

  const latestMid = sortedMids[0];

  // The following variable will be used when the merchant is master mid
  // in order to determine sequence for its child mids during the onboarding process
  let nextLongTerminalId = '';

  if (isMasterMid) {
    const latestNormalMid = normalMids.sort((a, b) => Number(b.internalMerchantId) - Number(a.internalMerchantId))[0];
    if (latestNormalMid) {
      const newSequence = Number(latestNormalMid.internalMerchantId.substring(5, 8)) + 1;
      nextLongTerminalId = `0${midConfiguration.pspNumber}0${newSequence.toString().padStart(3, '0')}${newSequence.toString().padStart(7, '0')}`;
    } else {
      nextLongTerminalId = `0${midConfiguration.pspNumber}00010000001`;
    }
    logger.info(`Next Long Terminal: ${nextLongTerminalId}`);
  }

  if (latestMid) {
    logger.info(`Latest MID: ${latestMid?.internalMerchantId}`);

    const newSequence = String(Number(latestMid.internalMerchantId.substring(5, 8)) + 1);
    newMid = `0${midConfiguration.pspNumber}${mastedMidFlag}${newSequence.padStart(3, '0')}`;
    newLongTerminalId = `${newMid}${newSequence.padStart(7, '0')}`;

    logger.info(`New Long Terminal: ${newLongTerminalId}`);
  } else {
    const extension = '1'.padStart(7, '0');
    newMid = `0${midConfiguration.pspNumber}${mastedMidFlag}001`;
    newLongTerminalId = `${newMid}${extension}`;

    logger.info(`No existing MIDs found under that psp. Configuring new MID...`);
    logger.info(`New Long Terminal: ${newLongTerminalId}`);
  }

  const finalMidConfiguration: NewMid = {
    longTerminal: newLongTerminalId,
    parentLongTerminal: parentTerminalId!,
    nextLongTerminal: nextLongTerminalId,
    isMasterMid: isMasterMid
  };

  responseMessage.result = finalMidConfiguration;

  await publishMessage('merchant-application-mid-response', 'new-mid', responseMessage);
}

async function formPspMid(id: string, midHierarchy: MidEntity[], midConfiguration: MidConfiguration): Promise<void> {
  const responseMessage = {
    acquiringEntityId: id,
    result: {}
  };

  const existingPsp = midHierarchy.find((mid) => mid.internalMerchantId.substring(2, 5) === midConfiguration.pspNumber);
  if (existingPsp) {
    logger.warn(`PSP already exists.`);
    responseMessage.result = {
      error: `PSP ${existingPsp.internalMerchantId} already exists.`
    };
    await publishMessage('merchant-application-mid-response', 'mid-error', responseMessage);
    return;
  }

  const newPspMid = `20${midConfiguration.pspNumber}`.padEnd(8, '0');
  const nesPspTerminal = newPspMid.padEnd(15, '0');

  const finalMidConfiguration: NewMid = {
    longTerminal: nesPspTerminal,
    parentLongTerminal: midConfiguration.midToBeAttached.padEnd(15, '0'),
    isMasterMid: false
  };

  responseMessage.result = finalMidConfiguration;

  await publishMessage('merchant-application-mid-response', 'new-mid', responseMessage);
}

// Checking if psp number doesn't exists on another psp
// Hence an error should be thrown
async function isPspReferenceCorrect(midToBeAttached: string, pspNumber: string): Promise<boolean> {
  const firstMid = `0${pspNumber}0001`;
  const firsMastertMid = `0${pspNumber}9001`;
  try {
    const midHierarchy = (await getAcquiringProfileCall(
      'merchant-hierarchy',
      `parentInternalMerchantId=${firstMid}&extractDirection=DOWN
`
    )) as MidEntity[];

    const masterMidHierarchy = (await getAcquiringProfileCall(
      'merchant-hierarchy',
      `parentInternalMerchantId=${firsMastertMid}&extractDirection=DOWN`
    )) as MidEntity[];

    //Checking if MID with the provided PSP number exists
    if (midHierarchy.length === 0 && masterMidHierarchy.length === 0) return true;

    const mids = [...midHierarchy, ...masterMidHierarchy];

    const filteredMid = mids.find(
      (mid) =>
        mid.internalMerchantId.substring(1, 4) === firstMid.substring(1, 4) &&
        mid.parentInternalMerchantId === midToBeAttached
    );

    // If we have a mid with the provided PSP number which exists
    // with the provided parent MID, then it is correct
    return filteredMid ? true : false;
  } catch {
    return true;
  }
}
