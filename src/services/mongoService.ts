import { aci<PERSON>ogger, omnipayLogger } from '../configs/logger';
import { Division, Merchant, Channel, MerchantAccount } from '../models/interfaces/aci';
import DivisionModel from '../models/schemas/divisionSchema';
import MerchantModel from '../models/schemas/merchantSchema';
import ChannelModel from '../models/schemas/channelSchema';
import MerchantAccountModel from '../models/schemas/merchantAccountSchema';
import {
  Reference,
  ServiceContractsAccountFee,
  ServiceContractsAccountSettlement,
  ServiceContractsAddressCategory,
  ServiceContracts,
  ServiceContractsServiceTariff,
  ServiceContractsTariff,
  ServiceContractsTxCharge,
  OmnipayMerchant,
  MerchantReferenceData,
  ScheduledMerchantUpdate
} from '../models/interfaces/omnipay';
import ReferenceModel from '../models/schemas/referenceSchema';
import TariffModel from '../models/schemas/tariffSchema';
import AccountSettlementModel from '../models/schemas/accountSettlementSchema';
import TxChargeModel from '../models/schemas/txChargeSchema';
import AccountFeeModel from '../models/schemas/accountFeeSchema';
import ServicesTariffModel from '../models/schemas/servicesTariffSchema';
import AddressCategoryModel from '../models/schemas/addressCategorySchema';
import { MerchantFileData } from '../models/interfaces/requestDetails';
import FileDataModel from '../models/schemas/fileDataSchema';
import { MongooseError } from 'mongoose';
import ServiceContractModel from '../models/schemas/serviceContractSchema';
import OmnipayMerchantModel from '../models/schemas/omnipayMerchantSchema';
import { OmnipayMerchantDetails } from '../models/interfaces/requestDetails';
import MerchantReferenceModel from '../models/schemas/merchantReferenceSchema';
import ScheduledMerchUpdateModel from '../models/schemas/scheduledMerchUpdateSchema';
import {
  AddAccountRequest,
  ModifyAccFeeRequest,
  OverrideAccFeeRequest,
  DeleteAccFeesRequest,
  ModifyAccountRequest,
  ModifyAddressRequest,
  AddAddressRequest,
  MerchantReferenceRequest,
  UpdateMerchantDetailsRequest,
  MerchContractDetailsRequest,
  TxChargeRequest,
  DeleteAddressRequest
} from '../models/types/omnipayMaintenance';
import { log } from 'console';
import { OnboardingMessage } from '../models/interfaces/kafka';
import OnboardingMessageModel from '../models/schemas/kafkaMessageSchema';

export async function insertFileData(template: string, merchantsData: MerchantFileData[]): Promise<string> {
  try {
    await FileDataModel.insertMany(
      merchantsData.map((merchant) => ({
        ...merchant,
        long_terminal_id: merchant.long_terminal_id.replace(/[^0-9]/g, ''),
        template
      }))
    );
    return 'OK';
  } catch (error: MongooseError | any) {
    const message = error.message;
    aciLogger.error(error.message);
    return message.includes('E11000') ? 'Merchant already exists in database' : 'Internal server error';
  }
}

export async function getAwaitingFilesData(): Promise<MerchantFileData[]> {
  const batch = await FileDataModel.find({ onboarded: false });
  return batch;
}

export async function markFileAsOnboarded(merchant: MerchantFileData): Promise<boolean> {
  try {
    await FileDataModel.findOneAndUpdate(
      {
        long_terminal_id: merchant.long_terminal_id,
        merchant_name: merchant.merchant_name,
        division_name: merchant.division_name,
        template: merchant.template
      },
      { $set: { onboarded: true } }
    );
    return true;
  } catch (error: any) {
    aciLogger.error(error);
    return false;
  }
}

export async function clearProcessedFiles(): Promise<boolean> {
  try {
    await FileDataModel.deleteMany({ onboarded: true });
    return true;
  } catch (error: any) {
    aciLogger.error(error);
    return false;
  }
}

export async function insertDivision(division: Division): Promise<boolean> {
  try {
    await DivisionModel.findOneAndUpdate({ name: division.name }, { $set: division }, { upsert: true, new: true });
    return true;
  } catch (error: any) {
    aciLogger.error(error);
    return false;
  }
}

export async function getDivision(divisionName: string): Promise<Division | null> {
  const division = await DivisionModel.findOne({ name: divisionName });

  return division;
}

export async function insertMerchant(merchant: Merchant): Promise<boolean> {
  try {
    await MerchantModel.findOneAndUpdate({ name: merchant.name }, { $set: merchant }, { upsert: true, new: true });
    return true;
  } catch (error: any) {
    aciLogger.error(error);
    return false;
  }
}

export async function getMerchantByName(divisionId: string, merchantName: string): Promise<Merchant | null> {
  const merchant = await MerchantModel.findOne({ divisionId: divisionId, name: merchantName });
  return merchant;
}

export async function insertMerchantAccount(merchantAccount: MerchantAccount): Promise<boolean> {
  try {
    await MerchantAccountModel.findOneAndUpdate(
      { id: merchantAccount.id },
      { $set: merchantAccount },
      { upsert: true, new: true }
    );
    return true;
  } catch (error: any) {
    aciLogger.error(error);
    return false;
  }
}

export async function getMerchantAccountsByMerchantId(merchantId: string): Promise<MerchantAccount[]> {
  const merchantAccounts = await MerchantAccountModel.find({ associatedMerchantId: merchantId });
  return merchantAccounts;
}

export async function insertChannel(channel: Channel): Promise<boolean> {
  try {
    await ChannelModel.findOneAndUpdate({ name: channel.name }, { $set: channel }, { upsert: true, new: true });
    return true;
  } catch (error: any) {
    aciLogger.error(error);
    return false;
  }
}

export async function getChannel(merchantId: string, channelName: string): Promise<Channel | null> {
  const channel = await ChannelModel.findOne({ sender: merchantId, name: channelName });
  return channel;
}

export async function insertReferences(references: Reference[]): Promise<boolean> {
  try {
    const bulkOps = references.map((reference) => ({
      updateOne: {
        filter: { referenceIndex: reference.referenceIndex, code: reference.code },
        update: { $set: reference },
        upsert: true
      }
    }));

    await ReferenceModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getReferencesByIndex(referenceIndex: string): Promise<Reference[]> {
  const reference = await ReferenceModel.find({ referenceIndex: referenceIndex });
  return reference;
}

export async function getReferences(): Promise<Reference[]> {
  const references = await ReferenceModel.find();
  return references;
}

export async function insertMerchantReferences(references: MerchantReferenceData[]): Promise<boolean> {
  try {
    const bulkOps = references.map((reference) => ({
      updateOne: {
        filter: { referenceIndex: reference.referenceIndex },
        update: { $set: reference },
        upsert: true
      }
    }));

    await MerchantReferenceModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getMerchantReferences(): Promise<MerchantReferenceData[]> {
  const references = await MerchantReferenceModel.find();
  return references;
}

export async function getMerchantReference(referenceIndex: string): Promise<MerchantReferenceData | null> {
  const reference = await MerchantReferenceModel.findOne({ referenceIndex: referenceIndex });
  return reference;
}

export async function insertServiceContracts(serviceContracts: ServiceContracts[]): Promise<boolean> {
  try {
    const bulkOps = serviceContracts.map((serviceContract) => ({
      updateOne: {
        filter: { serviceContractIndex: serviceContract.serviceContractIndex },
        update: { $set: serviceContract },
        upsert: true
      }
    }));

    await ServiceContractModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getListServiceContracts(): Promise<ServiceContracts[]> {
  const serviceContracts = await ServiceContractModel.find();
  return serviceContracts;
}

export async function getServiceContract(serviceContractIndex: string): Promise<ServiceContracts | null> {
  const serviceContract = await ServiceContractModel.findOne({ serviceContractIndex: serviceContractIndex });
  return serviceContract;
}

export async function insertTariffs(tariffs: ServiceContractsTariff[]): Promise<boolean> {
  try {
    const bulkOps = tariffs.map((tariff) => ({
      updateOne: {
        filter: { serviceContractIndex: tariff.serviceContractIndex, tariffObject: tariff.tariffObject },
        update: { $set: tariff },
        upsert: true
      }
    }));

    await TariffModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getTariffs(): Promise<ServiceContractsTariff[]> {
  const tariffs = await TariffModel.find();
  return tariffs;
}

export async function insertAccountSettlements(
  accountSettlements: ServiceContractsAccountSettlement[]
): Promise<boolean> {
  try {
    const bulkOps = accountSettlements.map((settlement) => ({
      updateOne: {
        filter: { uniqueId: settlement.uniqueId },
        update: { $set: settlement },
        upsert: true
      }
    }));

    await AccountSettlementModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getAccountSettlements(): Promise<ServiceContractsAccountSettlement[]> {
  const accountSettlements = await AccountSettlementModel.find();
  return accountSettlements;
}

export async function getFilteredAccountSettlements(
  serviceContractIndex: string,
  payload: OmnipayMerchantDetails
): Promise<ServiceContractsAccountSettlement[]> {
  const accountSettlementsList: ServiceContractsAccountSettlement[] = [];
  const currencies = payload.accountCurrencies;

  for (const currency of currencies) {
    const accountSettlements = await AccountSettlementModel.find({
      serviceContractIndex: serviceContractIndex,
      accountCurrency: currency,
      'settlementMethod.settlementMethodDescription': `${payload.settlementMethod} BG`,
      'postingTariff.postingTariffDescription': `GEN-${currency}`
    });

    accountSettlementsList.push(...accountSettlements);
  }

  return accountSettlementsList;
}

export async function insertTxCharges(txCharges: ServiceContractsTxCharge[]): Promise<boolean> {
  try {
    const bulkOps = txCharges.map((txCharge) => ({
      updateOne: {
        filter: { serviceContractIndex: txCharge.serviceContractIndex, recordIdNumber: txCharge.recordIdNumber },
        update: { $set: txCharge },
        upsert: true
      }
    }));

    await TxChargeModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getTxCharges(): Promise<ServiceContractsTxCharge[]> {
  const txCharges = await TxChargeModel.find();
  return txCharges;
}

export async function insertAccountFees(accountFees: ServiceContractsAccountFee[]): Promise<boolean> {
  try {
    const bulkOps = accountFees.map((accountFee) => ({
      updateOne: {
        filter: { serviceContractIndex: accountFee.serviceContractIndex, recordIdNumber: accountFee.recordIdNumber },
        update: { $set: accountFee },
        upsert: true
      }
    }));

    await AccountFeeModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getAccountFees(): Promise<ServiceContractsAccountFee[]> {
  const accountFees = await AccountFeeModel.find();
  return accountFees;
}

export async function insertServicesTariff(servicesTariff: ServiceContractsServiceTariff[]): Promise<boolean> {
  try {
    const bulkOps = servicesTariff.map((serviceTariff) => ({
      updateOne: {
        filter: { serviceContractIndex: serviceTariff.serviceContractIndex, serviceId: serviceTariff.serviceId },
        update: { $set: serviceTariff },
        upsert: true
      }
    }));

    await ServicesTariffModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getServicesTariffs(serviceContractIndex: string): Promise<ServiceContractsServiceTariff[]> {
  const servicesTariff = await ServicesTariffModel.find({ serviceContractIndex: serviceContractIndex });
  return servicesTariff;
}

export async function insertAddressCategories(addressCategories: ServiceContractsAddressCategory[]): Promise<boolean> {
  try {
    const bulkOps = addressCategories.map((addressCategory) => ({
      updateOne: {
        filter: {
          serviceContractIndex: addressCategory.serviceContractIndex,
          CategoryObject: addressCategory.CategoryObject
        },
        update: { $set: addressCategory },
        upsert: true
      }
    }));

    await AddressCategoryModel.bulkWrite(bulkOps);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getAddressCategories(): Promise<ServiceContractsAddressCategory[]> {
  const addressCategories = await AddressCategoryModel.find();
  return addressCategories;
}

export async function insertOmnipayMerchant(omnipayMerchant: OmnipayMerchant): Promise<boolean> {
  try {
    await OmnipayMerchantModel.create(omnipayMerchant);
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getOmnipayMerchantByNumber(externalMerchantId: string): Promise<OmnipayMerchant | null> {
  const omnipayMerchant = (await OmnipayMerchantModel.findOne({
    'detail.requiredDetails.externalMerchantId': externalMerchantId
  })
    .select('detail.requiredDetails.externalMerchantId detail.hierarchy.internalMerchantId')
    .lean()) as OmnipayMerchant;
  return omnipayMerchant;
}

export async function merchantUpdates(
  internalMerchantId: string,
  part: string,
  method: string,
  data: any
): Promise<boolean> {
  switch (part) {
    case 'merchantDetails':
      return await updateMerchantDetails(internalMerchantId, data);
    case 'merchantStatus':
      return await updateImmediateMerchantStatus(internalMerchantId, data);
    case 'accounts':
      return await updateMerchantAccounts(internalMerchantId, method, data);
    case 'references':
      return await updateMerchantReferences(internalMerchantId, method, data);

    default:
      omnipayLogger.warn(`Unsupported part: ${part}. Redirecting to scheduled updates...`);
      return await scheduledUpdates(internalMerchantId, part, method, data);
  }
}

async function scheduledUpdates(internalMerchantId: string, part: string, method: string, data: any): Promise<boolean> {
  switch (part) {
    case 'merchantStatusScheduled':
      return await updateScheduledMerchantStatus(internalMerchantId, method, data);
    case 'addresses':
      return await updateAddresses(internalMerchantId, method, data);
    case 'accountFees':
      return await updateAccountFees(internalMerchantId, method, data);
    case 'transactionCharges':
      return await updateTxCharges(internalMerchantId, method, data);
    case 'detail.contract':
      return await updateMerchantContractDetails(internalMerchantId, method, data);
    default:
      omnipayLogger.warn(`Unsupported part: ${part}. Skipping Mongo update flow...`);
      return true;
  }
}

async function updateMerchantAccounts(internalMerchantId: string, method: string, data: any): Promise<boolean> {
  try {
    let updateOperation: any;
    let filters: any = undefined;
    switch (method) {
      case 'PUT':
        const putData = data as ModifyAccountRequest[];
        updateOperation = {
          $set: putData.reduce(
            (acc, account) => ({
              ...acc,
              [`accounts.$[${account.internalAccountNumber}]`]: account
            }),
            {}
          )
        };
        filters = putData.map((account) => ({
          [`${account.internalAccountNumber}.internalAccountNumber`]: account.internalAccountNumber
        }));
        break;
      case 'POST':
        const postData = data as AddAccountRequest[];
        updateOperation = { $push: { accounts: { $each: postData } } };
        break;
      default:
        omnipayLogger.warn(`Unsupported method: ${method}. Skipping update...`);
        return true;
    }
    await OmnipayMerchantModel.updateOne(
      { 'detail.hierarchy.internalMerchantId': internalMerchantId },
      updateOperation,
      { arrayFilters: filters }
    );
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateMerchantReferences(internalMerchantId: string, method: string, data: any): Promise<boolean> {
  try {
    let updateOperation: any;
    let filters: any = undefined;
    switch (method) {
      case 'PUT':
        const putData = data as MerchantReferenceRequest[];
        updateOperation = {
          $set: putData.reduce(
            (acc, reference) => ({
              ...acc,
              [`references.$[${reference.referenceIndex}]`]: reference
            }),
            {}
          )
        };
        filters = putData.map((reference) => ({
          [`${reference.referenceIndex}.referenceIndex`]: reference.referenceIndex
        }));
        break;
      case 'POST':
        const postData = data as MerchantReferenceRequest[];
        updateOperation = { $push: { references: { $each: postData } } };
        break;
      case 'DELETE':
        const deleteData = data as MerchantReferenceRequest[];
        updateOperation = {
          $pull: { references: { referenceIndex: { $in: deleteData.map((ref) => ref.referenceIndex) } } }
        };
        break;
      default:
        omnipayLogger.warn(`Unsupported method: ${method}. Skipping update...`);
        return true;
    }

    await OmnipayMerchantModel.updateOne(
      { 'detail.hierarchy.internalMerchantId': internalMerchantId },
      updateOperation,
      { arrayFilters: filters }
    );
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateMerchantDetails(internalMerchantId: string, data: UpdateMerchantDetailsRequest): Promise<boolean> {
  try {
    // We need to update certain fields without affecting the rest of the document
    const flattenObject = (obj: any, prefix = 'detail'): Record<string, any> => {
      return Object.entries(obj).reduce((acc, [key, value]) => {
        const newKey = `${prefix}.${key}`;

        if (value && typeof value === 'object') {
          // If it's an array, don't flatten it further
          if (Array.isArray(value)) return { ...acc, [newKey]: value };

          // If it's an object, recursively flatten it
          return { ...acc, ...flattenObject(value, newKey) };
        }

        return { ...acc, [newKey]: value };
      }, {});
    };

    const updateOperations = flattenObject(data);

    await OmnipayMerchantModel.updateOne(
      { 'detail.hierarchy.internalMerchantId': internalMerchantId },
      { $set: updateOperations }
    );

    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateImmediateMerchantStatus(internalMerchantId: string, data: any): Promise<boolean> {
  try {
    await OmnipayMerchantModel.updateOne(
      { 'detail.hierarchy.internalMerchantId': internalMerchantId },
      { $push: { merchantStatus: data } }
    );
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

// Queries below are for scheduled (future) updates
async function updateScheduledMerchantStatus(internalMerchantId: string, method: string, data: any): Promise<boolean> {
  try {
    await ScheduledMerchUpdateModel.updateOne(
      { internalMerchantId: internalMerchantId },
      { $set: { merchantStatus: data } },
      { upsert: true }
    );
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateMerchantContractDetails(
  internalMerchantId: string,
  method: string,
  data: MerchContractDetailsRequest
): Promise<boolean> {
  try {
    switch (method) {
      case 'PUT':
        await ScheduledMerchUpdateModel.updateOne({ internalMerchantId }, { $set: { contract: data } });
        break;
      case 'POST':
        await ScheduledMerchUpdateModel.updateOne(
          { internalMerchantId },
          { $push: { contract: data } },
          { upsert: true }
        );
        break;
      case 'DELETE':
        await ScheduledMerchUpdateModel.updateOne({ internalMerchantId }, { $unset: { contract: 1 } });
        break;
      default:
        omnipayLogger.error(`Unsupported method: ${method}`);
        return false;
    }
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateAddresses(internalMerchantId: string, method: string, data: any): Promise<boolean> {
  try {
    let updateOperation: any;
    let filters: any = undefined;

    switch (method) {
      case 'PUT':
        const putData = data as ModifyAddressRequest[];
        updateOperation = {
          $set: putData.reduce(
            (acc, address) => ({
              ...acc,
              [`addresses.$[${address.categoryIndex}]`]: address
            }),
            {}
          )
        };
        filters = putData.map((address) => ({
          [`${address.categoryIndex}.categoryIndex`]: address.categoryIndex
        }));
        break;
      case 'POST':
        const postData = data as AddAddressRequest[];
        updateOperation = { $push: { addresses: { $each: postData } } };
        break;
      case 'DELETE':
        const deleteData = data as DeleteAddressRequest[];
        updateOperation = {
          $pull: { addresses: { categoryIndex: { $in: deleteData.map((addr) => addr.categoryIndex) } } }
        };
        break;
      default:
        omnipayLogger.warn(`Unsupported method: ${method}. Skipping update...`);
        return true;
    }
    await ScheduledMerchUpdateModel.updateOne({ internalMerchantId }, updateOperation, {
      arrayFilters: filters,
      upsert: true
    });
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateAccountFees(internalMerchantId: string, method: string, data: any): Promise<boolean> {
  try {
    let updateOperation: any;
    let filters: any = undefined;
    switch (method) {
      case 'PUT':
        const putData = data as ModifyAccFeeRequest[];
        updateOperation = {
          $set: putData.reduce(
            (acc, fee) => ({
              ...acc,
              [`merchantAccountFees.$[${fee.recordIdNumber}]`]: fee
            }),
            {}
          )
        };
        filters = putData.map((fee) => ({
          [`${fee.recordIdNumber}.recordIdNumber`]: fee.recordIdNumber
        }));
        break;
      case 'POST':
        const postData = data as OverrideAccFeeRequest[];
        updateOperation = { $push: { merchantAccountFees: { $each: postData } } };
        break;
      case 'DELETE':
        const deleteData = data as DeleteAccFeesRequest;
        updateOperation = { $pull: { merchantAccountFees: { recordIdNumber: { $in: deleteData.recordIdNumbers } } } };
        break;
      default:
        omnipayLogger.error(`Unsupported method: ${method}`);
        return false;
    }
    await ScheduledMerchUpdateModel.updateOne({ internalMerchantId }, updateOperation, {
      arrayFilters: filters,
      upsert: true
    });
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

async function updateTxCharges(internalMerchantId: string, method: string, data: any): Promise<boolean> {
  try {
    let updateOperation: any;
    let filters: any = undefined;
    switch (method) {
      case 'PUT':
      // Will reprice future dated transaction charges
      case 'POST':
        // Will reprice active transaction charges
        const putData = data as TxChargeRequest[];
        updateOperation = {
          $set: putData.reduce(
            (acc, fee) => ({
              ...acc,
              [`transactionCharges.$[${fee.recordIdNumber}]`]: fee
            }),
            {}
          )
        };
        filters = putData.map((fee) => ({
          [`${fee.recordIdNumber}.recordIdNumber`]: fee.recordIdNumber
        }));
        break;
      case 'DELETE':
        // Will delete future dated transaction charges only
        const deleteData = data as DeleteAccFeesRequest;
        updateOperation = { $pull: { transactionCharges: { recordIdNumber: { $in: deleteData.recordIdNumbers } } } };
        break;
      default:
        omnipayLogger.error(`Unsupported method: ${method}`);
        return false;
    }
    await OmnipayMerchantModel.updateOne(
      { 'detail.hierarchy.internalMerchantId': internalMerchantId },
      updateOperation,
      { arrayFilters: filters }
    );
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getScheduledUpdates(): Promise<ScheduledMerchantUpdate[]> {
  const scheduledMerchantUpdate = await ScheduledMerchUpdateModel.find();
  return scheduledMerchantUpdate;
}

export async function checkScheduledUpdates(scheduledMerchantUpdate: ScheduledMerchantUpdate): Promise<boolean> {
  try {
    const { internalMerchantId, merchantStatus, accountFees, addresses, contract, txCharges, paymentInstructions } =
      scheduledMerchantUpdate;
    const currentDate = new Date();
    const year = currentDate.getFullYear().toString();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const day = currentDate.getDate().toString().padStart(2, '0');
    const dateFormat = Number(`${year}${month}${day}`);
    let queryOperation = { $set: {}, $push: {} };

    // If either of the scheduled updates correspond to the current date
    // the according merchant should be updated with those values
    // and clear according scheduled updates

    if (Number(merchantStatus?.merchantStatusEffectiveDate) <= dateFormat) {
      const merchantStatusReferences = await getReferencesByIndex('MERCHANTSTATUSINDEX');
      const merchantStatusReference = merchantStatusReferences.find(
        (ref) => ref.code === merchantStatus.merchantStatusIndex
      );

      queryOperation.$set = { ...queryOperation.$set, merchantStatus: merchantStatusReference?.description };
      await ScheduledMerchUpdateModel.updateOne({ internalMerchantId }, { $unset: { merchantStatus: 1 } });
    }

    if (Number(contract?.effectiveDate) <= dateFormat) {
      queryOperation.$set = { ...queryOperation.$set, contract: contract };
      await ScheduledMerchUpdateModel.updateOne({ internalMerchantId }, { $unset: { contract: 1 } });
    }

    if (accountFees.some((fee) => Number(fee.effectiveDate) <= dateFormat)) {
      const effectiveFees = accountFees.filter((fee) => Number(fee.effectiveDate) === dateFormat);
      queryOperation.$push = { ...queryOperation.$push, merchantAccountFees: { $each: effectiveFees } };

      await ScheduledMerchUpdateModel.updateOne(
        { internalMerchantId },
        { $unset: { accountFees: { recordIdNumber: { $in: effectiveFees.map((fee) => fee.recordIdNumber) } } } }
      );
    }

    if (addresses.some((addr) => Number(addr.effectiveDate) <= dateFormat)) {
      const effectiveAddresses = addresses.filter((addr) => Number(addr.effectiveDate) === dateFormat);
      queryOperation.$push = { ...queryOperation.$push, addresses: { $each: effectiveAddresses } };

      await ScheduledMerchUpdateModel.updateOne(
        { internalMerchantId },
        { $unset: { addresses: { categoryIndex: { $in: effectiveAddresses.map((addr) => addr.categoryIndex) } } } }
      );
    }

    if (txCharges.some((charge) => Number(charge.effectiveDate) <= dateFormat)) {
      const effectiveCharges = txCharges.filter((charge) => Number(charge.effectiveDate) === dateFormat);
      queryOperation.$push = { ...queryOperation.$push, transactionCharges: { $each: effectiveCharges } };

      await ScheduledMerchUpdateModel.updateOne(
        { internalMerchantId },
        {
          $unset: { txCharges: { recordIdNumber: { $in: effectiveCharges.map((charge) => charge.recordIdNumber) } } }
        }
      );
    }

    if (paymentInstructions.some((instr) => Number(instr.deductionStartDate) <= dateFormat)) {
      const effectiveInstructions = paymentInstructions.filter(
        (instr) => Number(instr.deductionStartDate) <= dateFormat
      );
      queryOperation.$push = { ...queryOperation.$push, paymentInstructions: { $each: effectiveInstructions } };

      await ScheduledMerchUpdateModel.updateOne(
        { internalMerchantId },
        {
          $unset: {
            paymentInstructions: {
              deductionStartDate: { $in: effectiveInstructions.map((instr) => instr.deductionStartDate) }
            }
          }
        }
      );
    }

    await OmnipayMerchantModel.updateOne({ 'detail.hierarchy.internalMerchantId': internalMerchantId }, queryOperation);

    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function dropScheduledUpdates(internalMerchantId: string): Promise<boolean> {
  try {
    const scheduledMerchantUpdate = (await ScheduledMerchUpdateModel.findOne({
      internalMerchantId
    })) as ScheduledMerchantUpdate;

    if (!scheduledMerchantUpdate) return true;

    const { merchantStatus, accountFees, addresses, contract, txCharges, paymentInstructions } =
      scheduledMerchantUpdate;

    if (!merchantStatus && !accountFees && !addresses && !contract && !txCharges && !paymentInstructions) {
      await ScheduledMerchUpdateModel.deleteOne({ internalMerchantId });
    }

    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function insertOnboardingMessage(message: OnboardingMessage): Promise<any> {
  try {
    const existingMessage = await OnboardingMessageModel.findOne({
      acquiringEntityId: message.acquiringEntityId,
      reference: message.reference
    });
    if (existingMessage) {
      omnipayLogger.info(`Message already exists in database. Skipping insertion.`);
      return true;
    }

    await OnboardingMessageModel.create(message);
    return true;
  } catch (error: any) {
    throw new Error(error);
  }
}

export async function markOnboardingMessageAsProcessed(messageId: string, reference: string): Promise<boolean> {
  try {
    await OnboardingMessageModel.updateOne(
      { acquiringEntityId: messageId, reference: reference },
      { $set: { processed: true } }
    );
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}

export async function getOnboardingMessages(): Promise<OnboardingMessage[]> {
  const onboardingMessages = await OnboardingMessageModel.find({ processed: false });
  return onboardingMessages;
}

export async function clearOnboardingMessages(): Promise<boolean> {
  try {
    await OnboardingMessageModel.deleteMany({ processed: true });
    return true;
  } catch (error: any) {
    omnipayLogger.error(error);
    return false;
  }
}
