import { kafkaService } from '../../submodules/ryvyl-commons/services/kafkaService';
import { logger } from '../configs/logger';
import { OnboardingMessage } from '../models/interfaces/kafka';
import { insertOnboardingMessage } from './mongoService';

export async function establishKafkaConnection(): Promise<boolean> {
  try {
    await kafkaService.connect();
    return true;
  } catch (error) {
    logger.error(error);
    return false;
  }
}

export function subscribeToTopics() {
  const kafkaTopics = process.env.KAFKA_TOPICS?.split(',') ?? [];

  kafkaService.subscribe(kafkaTopics, false, async (topic, message) => {
    if (kafkaTopics.includes(topic)) {
      try {
        const onboardingMessage = {
          acquiringEntityId: message.id,
          reference: topic,
          result: message.result,
          processed: false
        } as OnboardingMessage;
        switch (topic) {
          case 'merchant-application-omnipay':
          case 'merchant-application-mid':
          case 'merchant-application-aci':
            logger.info(`Processing message for ${topic} topic`);

            await insertOnboardingMessage(onboardingMessage);
            break;
          default:
            logger.warn(`${topic} not implemented`);
            break;
        }

        // Add delay to respect rate limits
        await new Promise((resolve) => setTimeout(resolve, 2000));
      } catch (error: any) {
        logger.error(`Error processing message for topic ${topic}: ${error.message}`);

        const errorMessage = {
          acquiringEntityId: message.id,
          result: {
            error: error.message
          }
        };
        await publishMessage(`${topic}-response`, 'error', errorMessage);
      }
    } else {
      logger.warn(`Not subscribed for ${topic} topic`);
    }
  });
}

export async function publishMessage(topic: string, key: string, message: any) {
  try {
    await kafkaService.publish(topic, key, message);
    logger.info(`Message for ${topic} topic sent!`);
  } catch (error) {
    throw error;
  }
}
