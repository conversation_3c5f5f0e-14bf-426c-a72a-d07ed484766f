import axios, { AxiosResponse } from 'axios';
import {
  ECommerceIndCodeEnum,
  MerchantLevelCodeEnum,
  ResidencyFeeLevelCodeEnum,
  ResidentStatusCodeEnum
} from '../enums/omnipayEnums';
import * as Omnipay from '../models/types/omnipayOnboarding';
import {
  BaseResponse,
  MerchantReferenceResponse,
  OmnipayMerchant,
  OnboardingResponse,
  Reference,
  ReferenceDataResponse,
  ServiceContracts,
  ServiceContractsResponse,
  ServiceContractsSpecificResponse
} from '../models/interfaces/omnipay';
import { generateJws } from '../utils/crypto';
import { OmnipayRequestOptions } from '../models/types/requestOptions';
import { v4 as uuidv4 } from 'uuid';
import { logger, omnipayLogger } from '../configs/logger';
import { OMNIPAY_CONFIG } from '../configs/config';
import { ApiEnum } from '../enums/omnipayEnums';
import {
  getAddressCategories,
  getFilteredAccountSettlements,
  getMerchantReference,
  insertOmnipayMerchant,
  getListServiceContracts,
  getReferences,
  getServicesTariffs,
  getTariffs
} from './mongoService';
import { OmnipayMerchantDetails } from '../models/interfaces/requestDetails';
import { merchantOnboardingSchema } from '../utils/validation/omnipay/onboardingValidation';
import { handleOmniPayError, throwOmniPayError, OmniPayValidationError } from '../utils/errorHandling';

const options: OmnipayRequestOptions = {
  headers: {
    'Content-Type': 'application/json',
    AppKey: OMNIPAY_CONFIG.appKey,
    Environment: OMNIPAY_CONFIG.environment,
    'JWS-Signature': ''
  },
  params: {}
};

const SignatureSpecificHeaders: Record<ApiEnum, { audience: string; scope: string }> = {
  [ApiEnum.Boarding]: {
    audience: process.env.OMNIPAY_BOARDING_AUD!,
    scope: process.env.OMNIPAY_BOARDING_SCOPE!
  },
  [ApiEnum.AcquirerProfile]: {
    audience: process.env.OMNIPAY_ACQUIRER_PROFILE_AUD!,
    scope: process.env.OMNIPAY_ACQUIRER_PROFILE_SCOPE!
  },
  [ApiEnum.Maintenance]: {
    audience: process.env.OMNIPAY_MAINTENANCE_AUD!,
    scope: process.env.OMNIPAY_MAINTENANCE_SCOPE!!
  }
};

function updateOptions(api: ApiEnum, jws: string, internalMerchantId?: string, queryParams?: any) {
  options.headers['JWS-Signature'] = jws;
  options.params = queryParams;

  switch (api) {
    case ApiEnum.Boarding:
      options.headers.institutionNumber = OMNIPAY_CONFIG.institutionNumber;
      options.headers['Correlation-Id'] = uuidv4();
      break;
    case ApiEnum.AcquirerProfile:
    case ApiEnum.Maintenance:
      options.headers['Institution-Number'] = OMNIPAY_CONFIG.institutionNumber;
      options.headers['FSV-Interaction-Id'] = uuidv4();
      if (api === ApiEnum.Maintenance) options.headers['Internal-Merchant-Id'] = internalMerchantId;
      break;
  }

  return options;
}

export function incrementMidNumber(midNumber: string): string {
  const firstHalf = midNumber.substring(0, 8);
  const secondHalf = midNumber.substring(8, 15);

  // Increment each half and pad with leading zeros if needed
  const incrementedFirstHalf = (parseInt(firstHalf) + 1).toString().padStart(8, '0');
  const incrementedSecondHalf = (parseInt(secondHalf) + 1).toString().padStart(7, '0');
  const newMidNumber = incrementedFirstHalf + incrementedSecondHalf;
  return newMidNumber;
}

export async function formPayload(
  merchantLevelCode: MerchantLevelCodeEnum,
  mcc: string,
  url: string,
  hasBillingLevel: boolean,
  payload: OmnipayMerchantDetails,
  currency?: string
): Promise<string> {
  try {
    const references = await getReferences();
    const serviceContracts = await getListServiceContracts();

    const detail = await createMerchantDetails(references, serviceContracts, merchantLevelCode, mcc, url, payload);
    const addresses = await createAddresses(payload);
    const accounts = await createAccounts(serviceContracts, payload, hasBillingLevel, currency);
    const services =
      merchantLevelCode === MerchantLevelCodeEnum.MEMBER ? await createServices(serviceContracts) : undefined;
    const merchantReferences = await createMerchantReferences(payload.midConfig.longTerminal);

    const merchantToOnboard: Omnipay.MerchantToOnboard = {
      detail: detail,
      addresses: addresses,
      accounts: accounts,
      services: services,
      references: merchantReferences
    };

    const { error } = merchantOnboardingSchema.validate(merchantToOnboard, { abortEarly: false });

    if (error) {
      const errorMessages = error.details.map((err) => err.message.replace(/"/g, ''));
      omnipayLogger.error(`Validation errors: ${errorMessages.join(', ')}`);
      throw new OmniPayValidationError('Merchant validation failed', errorMessages);
    }

    const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.Boarding], 'POST', merchantToOnboard);
    const updatedOptions = updateOptions(ApiEnum.Boarding, jws);

    const validationResponse = await onboardToOmnipay('VALIDATE', updatedOptions, merchantToOnboard);
    const validation = validationResponse?.data as OnboardingResponse;

    if (validationResponse?.status === 202) {
      omnipayLogger.info('Validation successful, creating merchant...');

      const createResponse = await onboardToOmnipay('CREATE', updatedOptions, merchantToOnboard);
      const createData = createResponse?.data as OnboardingResponse;
      const resultMessage = await formOnboardingResponseMessage(merchantToOnboard as OmnipayMerchant, createData);

      return resultMessage;
    } else {
      return validation
        ? await formOnboardingResponseMessage(merchantToOnboard as OmnipayMerchant, validation)
        : 'Service not responding';
    }
  } catch (error: any) {
    throwOmniPayError(error, 'formPayload');
  }
}

async function onboardToOmnipay(
  action: string,
  options: OmnipayRequestOptions,
  payload: Omnipay.MerchantToOnboard
): Promise<AxiosResponse | undefined> {
  try {
    const response = await axios.post(
      `${OMNIPAY_CONFIG.baseUrl}/boarding/v1.0/merchants?intent=${action}`,
      payload,
      options
    );

    return response;
  } catch (error: any) {
    throw error;
  }
}

async function createMerchantDetails(
  references: Reference[],
  serviceContracts: ServiceContracts[],
  merchantLevelCode: MerchantLevelCodeEnum,
  mcc: string,
  url: string,
  payload: OmnipayMerchantDetails
): Promise<Omnipay.MerchantDetail> {
  const hierarchy = createHierarchy(merchantLevelCode, payload);
  const requiredDetails = createRequiredDetails(references, mcc, payload);
  const optionalDetails = createOptionalDetails(payload);
  const contract = await createContract(payload, references, serviceContracts);
  const location = createLocation(url, payload);

  const merchantDetails: Omnipay.MerchantDetail = {
    hierarchy,
    requiredDetails,
    optionalDetails,
    contract,
    location
  };

  return merchantDetails;
}

async function createAddresses(payload: OmnipayMerchantDetails): Promise<Omnipay.MerchantAddress[]> {
  const addresses: Omnipay.MerchantAddress[] = [];
  const addressCategories = (await getAddressCategories()).filter((ac) => ac.CategoryObject.categoryIndex !== '003');

  await Promise.all(
    addressCategories.map(async (addressCategory) => {
      const merchantAddress: Omnipay.MerchantAddress = {
        categoryIndex: addressCategory?.CategoryObject?.categoryIndex!,
        groupSpecific: false,
        line1: payload.address,
        city: payload.merchantCity,
        countryCode: payload.countryCode,
        postCode: payload.postCode,
        telephone: payload.telephone,
        email: payload.email,
        deliveryMethodIndex: '000'
      };

      addresses.push(merchantAddress);
    })
  );

  return addresses;
}

async function createAccounts(
  serviceContracts: ServiceContracts[],
  payload: OmnipayMerchantDetails,
  hasBillingLevel: boolean,
  currency?: string
): Promise<Omnipay.MerchantAccount[]> {
  const serviceContractIndexCodes = serviceContracts.map((sc) => sc.serviceContractIndex);
  const serviceContractIndex = serviceContractIndexCodes[0];
  const accounts: Omnipay.MerchantAccount[] = [];

  // Will get only those account settlements with matching settlement method and posting tariff
  // Will push all those accounts at the moment
  const accountSettlements = await getFilteredAccountSettlements(serviceContractIndex, payload);

  await Promise.all(
    accountSettlements
      .filter((as) => (currency ? as.accountCurrency === currency : true))
      .map(async (accountSettlement) => {
        const merchantAccount: Omnipay.MerchantAccount = {
          accountTypeIndex: accountSettlement.accountType.accountTypeIndex,
          accountCurrencyCode: accountSettlement.accountCurrency,
          billingLevel: hasBillingLevel,
          receiverCountryCode: hasBillingLevel ? payload.countryCode : undefined,
          payable: {
            accountNumber: payload.accountNumber,
            iban: payload.iban,
            correspondentBankNo: payload.bic,
            bankName: payload.bankName,
            bankCity: payload.bankCity,
            bankContactName: payload.companyName,
            accountName: payload.tradeName
          },
          receivable: {
            accountNumber: payload.accountNumber,
            iban: payload.iban,
            correspondentBankNo: payload.bic,
            bankName: payload.bankName,
            bankCity: payload.bankCity,
            bankContactName: payload.companyName,
            accountName: payload.tradeName
          }
        };
        accounts.push(merchantAccount);
      })
  );

  return accounts;
}

async function createServices(serviceContracts: ServiceContracts[]): Promise<Omnipay.Services[]> {
  const serviceTariffs = await getServicesTariffs(serviceContracts[0].serviceContractIndex);
  const services: Omnipay.Services[] = [];

  await Promise.all(
    serviceTariffs.map(async (serviceTariff) => {
      const service: Omnipay.Services = {
        serviceIndex: serviceTariff.serviceId.serviceIndex,
        assigned: true,
        tariffIndex: serviceTariff.serviceMerchantTariffs.tariffIndex
      };
      services.push(service);
    })
  );

  return services;
}

async function createMerchantReferences(value: string): Promise<Omnipay.MerchantReference[]> {
  // As per requirements we will use only reference 001 - Merchant Number
  const merchantReference = await getMerchantReference('001');

  if (!merchantReference) {
    logger.warn('Could not find merchant reference');
    return [];
  }

  return [{ referenceIndex: merchantReference.referenceIndex, referenceValue: value }];
}

function createHierarchy(merchantLevelCode: MerchantLevelCodeEnum, payload: OmnipayMerchantDetails): Omnipay.Hierarchy {
  return {
    internalMerchantId: payload.midConfig.longTerminal.substring(0, 8),
    merchantLevelCode,
    parentInternalMerchantId: payload.midConfig.parentLongTerminal.substring(0, 8)
  };
}

function createRequiredDetails(
  references: Reference[],
  mcc: string,
  payload: OmnipayMerchantDetails
): Omnipay.RequiredDetails {
  // Find reference codes by filtering and matching descriptions
  const findReferenceCode = (index: string, description?: string, defaultCode = '000') => {
    const items = references.filter((ref) => ref.referenceIndex === index);
    if (!description) return defaultCode;
    const match = items.find((item) => item.description.toLocaleLowerCase() === description.toLocaleLowerCase());
    return match?.code ?? defaultCode;
  };

  // Determine merchant type code
  const merchantType =
    payload.merchantType === 'Traditional'
      ? ECommerceIndCodeEnum.TRADITIONAL_ONLY
      : payload.merchantType === 'Ecommerce'
        ? ECommerceIndCodeEnum.ECOMMERCE_ONLY
        : ECommerceIndCodeEnum.BOTH;

  // Determine resident status code
  const residentStatusCode = !payload.residentStatus
    ? ResidentStatusCodeEnum.NA
    : payload.residentStatus === 'Resident'
      ? ResidentStatusCodeEnum.RESIDENT
      : ResidentStatusCodeEnum.NON_RESIDENT;

  // Return the required details object
  return {
    externalMerchantId: payload.midConfig.longTerminal,
    tradeName: payload.tradeName,
    companyName: payload.tradeName,
    legalFormIndex: findReferenceCode('LEGALFORMINDEX', payload.legalForm),
    languageIndex: findReferenceCode('LANGUAGEINDEX', payload.language, '001'),
    mcciso: mcc,
    eCommerceIndCode: merchantType,
    residentStatusCode,
    accountOfficerIndex: findReferenceCode('ACCOUNTOFFICERINDEX'),
    residencyFeeLevelCode: ResidencyFeeLevelCodeEnum.NA,
    retailerClassificationCode: findReferenceCode('RETAILERCLASSIFICATION', payload.rcc, '798'),
    branchIndex: findReferenceCode('BRANCHINDEX')
  };
}

function createOptionalDetails(payload: OmnipayMerchantDetails): Omnipay.OptionalDetails {
  return {
    registrationNo: payload.registrationNumber,
    vatRegNo: payload.vatRegistrationNumber
  };
}

async function createContract(
  payload: OmnipayMerchantDetails,
  references: Reference[],
  serviceContracts: ServiceContracts[]
): Promise<Omnipay.Contract> {
  const serviceContractIndexCode = serviceContracts.map((sc) => sc.serviceContractIndex)[0];
  const clientTariffs = await getTariffs();
  const clientTariff = clientTariffs.filter((tariff) => tariff.serviceContractIndex === serviceContractIndexCode)[0];
  // Will get only those account settlements with matching settlement method and posting tariff
  // Will use that filter to configure all accounts that needs to be added later
  const accountSettlements = await getFilteredAccountSettlements(serviceContractIndexCode, payload);
  // Will get only single one to populate posting tariff and settlement method
  const accountSettlement = accountSettlements[0];
  const clientRegions = references.filter((reference) => reference.referenceIndex === 'CLIENTREGIONINDEX');
  const clientRegionCode = clientRegions.find((region) => region.description === payload.clientRegion)?.code ?? '902';

  const contract: Omnipay.Contract = {
    serviceContractIndex: serviceContractIndexCode ?? '',
    clientTariffIndex: clientTariff.tariffObject.clientTariffIndex ?? '',
    postingTariffIndex: accountSettlement?.postingTariff?.postingTariffIndex ?? '',
    settlementMethodIndex: accountSettlement?.settlementMethod?.settlementMethodIndex ?? '',
    protectAgainstFXChange: false,
    clientRegionIndex: clientRegionCode
  };

  return contract;
}

function createLocation(url: string, payload: OmnipayMerchantDetails): Omnipay.Location {
  return {
    countryCode: payload.countryCode,
    cityUrl: payload.merchantCity,
    url: url
  };
}

export async function getReferenceData(): Promise<Reference[] | any> {
  const references: Reference[] = [];
  const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.AcquirerProfile], 'GET');
  const updatedOptions = updateOptions(ApiEnum.AcquirerProfile, jws);
  const limit = 100;
  let offset = 0;
  let totalPages = 0;
  let requestCount = 0;

  try {
    // First request to get total pages
    const initialResponse = await axios.get(
      `${OMNIPAY_CONFIG.baseUrl}/acquirerprofile/v1.0/reference-data?offset=${offset}&limit=${limit}`,
      updatedOptions
    );

    const initialData: ReferenceDataResponse = initialResponse.data;

    if (initialData.errors) {
      initialData.errors.forEach((error) => {
        omnipayLogger.warn(`${error.code} ${error.detail}`);
      });
      return references;
    }

    // Get total pages from the first response
    totalPages = initialData.meta.paging.totalPages ?? 0;
    omnipayLogger.info(`Total pages: ${totalPages}`);

    // Process first batch of data
    processReferenceData(initialData, references);
    requestCount++;
    offset += limit;

    // Continue fetching data until we've processed all pages
    while (requestCount < totalPages) {
      // Pause for 5 seconds after every 5 requests
      if (requestCount % 5 === 0) {
        omnipayLogger.info(`Pausing for 2 seconds after ${requestCount} requests...`);
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      const response = await axios.get(
        `${OMNIPAY_CONFIG.baseUrl}/acquirerprofile/v1.0/reference-data?offset=${offset}&limit=${limit}`,
        updatedOptions
      );

      const referenceData: ReferenceDataResponse = response.data;

      if (referenceData.errors)
        referenceData.errors.forEach((error) => {
          omnipayLogger.warn(`${error.code} ${error.detail}`);
        });
      else processReferenceData(referenceData, references);

      requestCount++;
      offset += limit;
      omnipayLogger.info(`Processed page ${requestCount}/${totalPages}`);
    }

    omnipayLogger.info(`Completed fetching all reference data with ${requestCount} requests`);
  } catch (error: any) {
    const errorResponse = handleOmniPayError(error, 'getReferenceData');
    return { error: errorResponse, updatedOptions };
  }

  return references;
}

export async function getMerchantReferencesData(): Promise<MerchantReferenceResponse> {
  try {
    const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.AcquirerProfile], 'GET');
    const updatedOptions = updateOptions(ApiEnum.AcquirerProfile, jws);

    const response = await axios.get(
      `${OMNIPAY_CONFIG.baseUrl}/acquirerprofile/v1.0/merchant-references`,
      updatedOptions
    );
    const merchantReferencesData: MerchantReferenceResponse = response.data;
    return merchantReferencesData;
  } catch (error: any) {
    return handleOmniPayError(error, 'getMerchantReferencesData') as MerchantReferenceResponse;
  }
}

// Helper function to process reference data and add to references array
function processReferenceData(referenceData: ReferenceDataResponse, references: Reference[]): void {
  referenceData.data.forEach((item: any) =>
    references.push({
      referenceIndex: item.classification,
      code: item.code,
      description: item.description
    } as Reference)
  );
}

export async function getServiceContracts(): Promise<ServiceContractsResponse> {
  const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.AcquirerProfile], 'GET');
  const updatedOptions = updateOptions(ApiEnum.AcquirerProfile, jws);
  try {
    const response = await axios.get(
      `${OMNIPAY_CONFIG.baseUrl}/acquirerprofile/v1.0/service-contracts`,
      updatedOptions
    );
    const serviceContractsData: ServiceContractsResponse = response.data;
    return serviceContractsData;
  } catch (error: any) {
    return handleOmniPayError(error, 'getServiceContracts') as ServiceContractsResponse;
  }
}

export async function getServiceContractsSpecific<R>(
  serviceContractIndex: string,
  endpoint: string,
  hasParams: boolean = true
): Promise<R[]> {
  const result: R[] = [];
  const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.AcquirerProfile], 'GET');
  const updatedOptions = updateOptions(ApiEnum.AcquirerProfile, jws);
  const limit = 100;
  let offset = 0;
  let totalPages = 0;
  let requestCount = 0;
  const baseUrl = `${OMNIPAY_CONFIG.baseUrl}/acquirerprofile/v1.0/service-contracts/${serviceContractIndex}/${endpoint}`;

  try {
    // First request to get total pages
    const initialParams = hasParams ? `?offset=${offset}&limit=${limit}` : '';
    const initialResponse = await axios.get(`${baseUrl}${initialParams}`, updatedOptions);
    const initialData: ServiceContractsSpecificResponse = initialResponse.data;

    if (initialData.errors) {
      initialData.errors.forEach((error) => omnipayLogger.warn(`${error.code} ${error.detail}`));
      return result;
    }

    // Get total pages and process first batch
    if (hasParams) totalPages = initialData.meta.paging.totalPages ?? 0;
    initialData.data.forEach((item) => result.push(item as R));
    requestCount++;
    offset += limit;

    // Continue fetching data until we've processed all pages
    while (requestCount < totalPages) {
      // Pause for 2 seconds after every 5 requests
      if (requestCount % 5 === 0) await new Promise((resolve) => setTimeout(resolve, 2000));

      const params = hasParams ? `?offset=${offset}&limit=${limit}` : '';
      const response = await axios.get(`${baseUrl}${params}`, updatedOptions);
      const data: ServiceContractsSpecificResponse = response.data;

      if (!data.errors) {
        data.data.forEach((item) => result.push(item as R));
      } else {
        data.errors.forEach((error) => omnipayLogger.warn(`${error.code} ${error.detail}`));
      }

      requestCount++;
      offset += limit;
    }

    return result;
  } catch (error: any) {
    throwOmniPayError(error, 'getServiceContractsSpecific');
  }
}

export async function maintenanceCall(
  method: string,
  endpoint: string,
  internalMerchantId: string,
  queryParams?: any,
  payload?: any
): Promise<BaseResponse> {
  try {
    const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.Maintenance], method, payload);
    const updatedOptions = updateOptions(ApiEnum.Maintenance, jws, internalMerchantId, queryParams);
    const actionMethod = axiosMethod(method);
    let response: AxiosResponse<any, any>;

    switch (method.toUpperCase()) {
      case 'GET':
        response = await actionMethod(`${OMNIPAY_CONFIG.baseUrl}/maintenance/v1.0/${endpoint}`, updatedOptions);
        break;
      case 'DELETE':
        response = await actionMethod(`${OMNIPAY_CONFIG.baseUrl}/maintenance/v1.0/${endpoint}`, {
          data: payload,
          headers: updatedOptions.headers
        });
        break;
      default:
        response = await actionMethod(
          `${OMNIPAY_CONFIG.baseUrl}/maintenance/v1.0/${endpoint}`,
          payload,
          updatedOptions
        );
    }

    return response.data as BaseResponse;
  } catch (error: any) {
    throwOmniPayError(error, 'maintenanceCall');
  }
}

export async function getAcquiringProfileCall(path: string, dynamicParams: string): Promise<any[]> {
  const jws = await generateJws(SignatureSpecificHeaders[ApiEnum.AcquirerProfile], 'GET');
  const updatedOptions = updateOptions(ApiEnum.AcquirerProfile, jws);
  const baseUrl = `${OMNIPAY_CONFIG.baseUrl}/acquirerprofile/v1.0/${path}?${dynamicParams}`;

  const limit = path === 'fx-rate' ? 100 : 1000;
  let offset = 0;
  let totalPages = 0;
  let requestCount = 0;
  const result: any[] = [];

  try {
    const initialParams = `offset=${offset}&limit=${limit}`;
    const initialResponse = await axios.get(`${baseUrl}&${initialParams}`, updatedOptions);
    const initialData: any = initialResponse.data;

    if (initialData.errors) {
      initialData.errors.forEach((error: any) => omnipayLogger.warn(`${error.code} ${error.detail}`));
      return result;
    }

    totalPages = initialData.meta.paging.totalPages ?? 0;
    initialData.data.forEach((item: any) => {
      if (path === 'fx-rate' && !item.counterCurrencyCode) item.counterCurrencyCode = '';
      result.push(item);
    });
    requestCount++;
    offset += limit;

    while (requestCount < totalPages) {
      // Pause for 2 seconds after every 5 requests
      if (requestCount % 5 === 0) await new Promise((resolve) => setTimeout(resolve, 2000));

      const params = `offset=${offset}&limit=${limit}`;
      const response = await axios.get(`${baseUrl}&${params}`, updatedOptions);
      const data: any = response.data;

      if (!data.errors)
        data.data.forEach((item: any) => {
          // When extracting fx rates keep counter currency code empty if undefined
          if (path === 'fx-rate' && !item.counterCurrencyCode) item.counterCurrencyCode = '';
          result.push(item);
        });
      else data.errors.forEach((error: any) => omnipayLogger.warn(`${error.code} ${error.detail}`));

      requestCount++;
      offset += limit;
    }

    return result;
  } catch (error: any) {
    throwOmniPayError(error, 'acquiringProfileCall');
  }
}

function axiosMethod(method: string) {
  const methods = {
    GET: axios.get,
    POST: axios.post,
    PUT: axios.put,
    DELETE: axios.delete,
    PATCH: axios.patch
  };

  return methods[method.toUpperCase() as keyof typeof methods];
}

export async function formOnboardingResponseMessage(
  onboardingPayload: OmnipayMerchant,
  onboardingResponse: OnboardingResponse
): Promise<string> {
  const { data, errors, meta } = onboardingResponse;

  if (errors) return errors.map((error) => `${error.code} - ${error.title}`).join('\n');

  if (data) {
    const mongoResult = await insertOmnipayMerchant(onboardingPayload);
    if (!mongoResult) {
      omnipayLogger.error(
        `Failed to insert merchant ${onboardingPayload.detail.hierarchy.internalMerchantId} into MongoDB`
      );
    }
    omnipayLogger.info('Creation successful. Merchant application sent.');
    return data.applicationNumber;
  }
  return meta.messages[0].detail;
}
