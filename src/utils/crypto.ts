import { createHash } from 'crypto';
import fs from 'fs';
import { importPKCS8, CompactSign } from 'jose';
import { logger } from '../configs/logger';

export async function generateJws(
  { audience, scope }: { audience: string; scope: string },
  method: string,
  payload?: any
) {
  try {
    const certPem = fs.readFileSync(process.env.OMNIPAY_JWS_CERT!, 'utf8');
    const privateKeyPem = fs.readFileSync(process.env.OMNIPAY_JWS_PRIV_KEY!, 'utf8');
    const privateKey = await importPKCS8(privateKeyPem, 'RS256');
    const kid = createHash('sha256').update(certPem).digest('base64url');

    // Create the protected header
    const protectedHeader = {
      alg: 'RS256',
      kid: kid,
      iss: 'RYVYL EU EAD',
      aud: audience,
      exp: Math.floor(Date.now() / 1000) + 60 * 60,
      scope: scope.replace('method', method),
      crit: ['aud', 'exp', 'iss']
    };

    // Create the payload string
    const payloadStr = JSON.stringify(payload);

    const jws = await new CompactSign(new TextEncoder().encode(payloadStr))
      .setProtectedHeader(protectedHeader)
      .sign(privateKey, { crit: { aud: true, exp: true, iss: true } });

    const [header, , signature] = jws.split('.');

    logger.info(`Generated detached JWS for ${audience} API`);
    return `${header}..${signature}`;
  } catch (error) {
    logger.error(`Error generating JWS: ${error}`);
    throw error;
  }
}
