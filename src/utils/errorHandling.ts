import { logger, omnipayLogger } from '../configs/logger';
import { isAxiosError } from 'axios';

// Define error response structure based on OmniPay API
export interface OmniPayError {
  code: string;
  title: string;
  detail?: string;
}

export interface OmniPayErrorResponse {
  errors?: OmniPayError[];
}

// Custom error classes for different error types
export class OmniPayApiError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode?: number,
    public readonly errors?: OmniPayError[]
  ) {
    super(message);
    this.name = 'OmniPayApiError';
  }
}

export class OmniPayValidationError extends Error {
  constructor(
    message: string,
    public readonly validationErrors: string[]
  ) {
    super(message);
    this.name = 'OmniPayValidationError';
  }
}

/**
 * Handles OmniPay API errors in a standardized way
 * @param error The error object
 * @param context The context where the error occurred (function name)
 * @returns A standardized error response
 */
export function handleOmniPayError(error: any, context: string): OmniPayErrorResponse {
  // For Axios errors (API responses with error status)
  if (isAxiosError(error) && error.response) {
    const errorResponse = error.response.data as OmniPayErrorResponse;

    // Log errors in a consistent format
    if (errorResponse.errors && errorResponse.errors.length > 0) {
      errorResponse.errors.forEach((err) => {
        omnipayLogger.error(
          `[${context}] API Error ${error.response?.status}: ${err.code} - ${err.title} ${
            err.detail ? `- ${err.detail}` : ''
          }`
        );
      });
    } else {
      // Handle case where response doesn't match expected format
      omnipayLogger.error(`[${context}] API Error ${error.response?.status}: ${error.message}`);
    }

    // Return sanitized error response (remove sensitive details if needed)
    return {
      errors: errorResponse.errors?.map((err) => ({
        code: err.code,
        title: err.title
        // Exclude detail for security in responses sent to clients
      }))
    };
  }

  // For non-Axios errors (runtime errors, etc.)
  omnipayLogger.error(`[${context}] Unexpected error: ${error.message || 'Unknown error'}`);

  // Return a generic error for non-API errors
  return {
    errors: [
      {
        code: 'INTERNAL_ERROR',
        title: 'An unexpected error occurred'
      }
    ]
  };
}

/**
 * Helper to throw standardized errors
 * @param error The error object
 * @param context The context where the error occurred
 */
export function throwOmniPayError(error: any, context: string): never {
  if (error instanceof OmniPayValidationError) throw new OmniPayValidationError(error.message, error.validationErrors);

  const errorResponse = handleOmniPayError(error, context);
  throw new OmniPayApiError(
    errorResponse.errors?.[0]?.title || 'API Error',
    errorResponse.errors?.[0]?.code || 'UNKNOWN_ERROR',
    isAxiosError(error) ? error.response?.status : undefined,
    errorResponse.errors
  );
}
