import Joi from 'joi';
import * as OmnipayEnums from '../../../enums/omnipayEnums';

const dateValidator = (value: string, helpers: Joi.CustomHelpers) => {
  const year = parseInt(value.substring(0, 4));
  const month = parseInt(value.substring(4, 6));
  const day = parseInt(value.substring(6, 8));
  const date = new Date(year, month - 1, day);

  if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
    return helpers.error(value);
  }
  return value;
};

const amountValidator = (value: string, helpers: Joi.CustomHelpers) => {
  const amount = parseFloat(value);
  if (isNaN(amount) || amount < 0) {
    return helpers.error('amount.invalid');
  }
  return value;
};

// Base schemas for reusable components
const globalFeeSchema = Joi.object({
  feeIndex: Joi.string().length(3).required(),
  feeMode: Joi.boolean().required(),
  cardOrganization: Joi.string().length(3).required()
});

const payableSchema = Joi.object({
  bankName: Joi.string().min(0).max(40).optional().allow(null),
  accountName: Joi.string().min(0).max(35),
  counterBankNumber: Joi.string().min(0).max(22),
  accountNumber: Joi.string().min(0).max(35),
  iban: Joi.string().min(0).max(35),
  bankTelephone: Joi.string().min(0).max(15),
  bankContactName: Joi.string().min(0).max(35),
  correspondentBankNo: Joi.string().min(0).max(11).optional().allow(null),
  customerPaymentText: Joi.string().min(0).max(18),
  bankCity: Joi.string().min(0).max(35),
  paymentFormatIndex: Joi.string().length(3),
  clearingEntityIndex: Joi.string().length(3),
  correspondentBankAccount: Joi.string().min(0).max(16),
  calendarDays: Joi.string().length(3),
  paymentDelayDays: Joi.string().length(3),
  ceilingLimit: Joi.string()
    .pattern(/^\d{1,11}\.\d{4}$/)
    .custom(amountValidator, 'Ceiling limit must be a valid amount')
    .min(0)
    .max(16)
});

const receivableSchema = Joi.object({
  bankName: Joi.string().min(0).max(40).optional().allow(null),
  accountName: Joi.string().min(0).max(35),
  counterBankNumber: Joi.string().min(0).max(22),
  accountNumber: Joi.string().min(0).max(35),
  iban: Joi.string().min(0).max(35),
  bankTelephone: Joi.string().min(0).max(15),
  bankContactName: Joi.string().min(0).max(35),
  correspondentBankNo: Joi.string().min(0).max(11).optional().allow(null),
  customerPaymentText: Joi.string().min(0).max(18),
  bankCity: Joi.string().min(0).max(35),
  paymentFormatIndex: Joi.string().length(3),
  clearingEntityIndex: Joi.string().length(3),
  correspondentBankAccount: Joi.string().min(0).max(16),
  calendarDays: Joi.string().length(3),
  collectionDelayDays: Joi.string().length(3),
  ddMandateReference: Joi.string().length(35)
});

// Main component schemas
const hierarchySchema = Joi.object({
  internalMerchantId: Joi.string().length(8).required(),
  merchantLevelCode: Joi.string()
    .valid(...Object.values(OmnipayEnums.MerchantLevelCodeEnum))
    .required(),
  parentInternalMerchantId: Joi.string().length(8)
});

const requiredDetailsSchema = Joi.object({
  externalMerchantId: Joi.string().min(0).max(20).required(),
  tradeName: Joi.string().min(0).max(22).required(),
  companyName: Joi.string().min(0).max(35).required(),
  legalFormIndex: Joi.string().length(3).required(),
  languageIndex: Joi.string().length(3).required(),
  mcciso: Joi.string().length(4).required(),
  eCommerceIndCode: Joi.string()
    .valid(...Object.values(OmnipayEnums.ECommerceIndCodeEnum))
    .required(),
  residentStatusCode: Joi.string()
    .valid(...Object.values(OmnipayEnums.ResidentStatusCodeEnum))
    .required(),
  accountOfficerIndex: Joi.string().required(),
  residencyFeeLevelCode: Joi.string()
    .valid(...Object.values(OmnipayEnums.ResidencyFeeLevelCodeEnum))
    .required(),
  retailerClassificationCode: Joi.string().length(3).required(),
  branchIndex: Joi.string().length(3).required()
});

const optionalDetailsSchema = Joi.object({
  registrationNo: Joi.string().min(0).max(15),
  vatRegNo: Joi.string().min(0).max(15),
  contractReference: Joi.string().min(0).max(8),
  bankReference: Joi.string().min(0).max(8),
  floorLimitCurrencyCode: Joi.string().length(3),
  dynamicPricing: Joi.boolean(),
  paymentAdviceGenerationIndex: Joi.string().length(3),
  paymentDeliveryMethodCode: Joi.string().valid(...Object.values(OmnipayEnums.PaymentDeliveryMethodCodeEnum)),
  merchantGradeIndex: Joi.string().length(3),
  secondaryTaxID: Joi.string().length(15),
  prosaDeterminant: Joi.string().length(8),
  prosaBDUNumber: Joi.string().length(8),
  signerDateOfBirth: Joi.string().length(8),
  merchantLongName: Joi.string().min(0).max(160),
  mcpRateType: Joi.string().length(1)
});

const contractSchema = Joi.object({
  serviceContractIndex: Joi.string().length(3).required(),
  clientTariffIndex: Joi.string().length(6).required(),
  postingTariffIndex: Joi.string().length(3).required(),
  settlementMethodIndex: Joi.string().length(3).required(),
  paymentMethodIndex: Joi.string().length(3),
  protectAgainstFXChange: Joi.boolean().required(),
  clientRegionIndex: Joi.string().length(3).required(),
  tierStructureIndex: Joi.string().length(3),
  reclassBillbackIndicatorCode: Joi.string().valid(...Object.values(OmnipayEnums.ReclassBillbackIndicatorCodeEnum)),
  withholdIncomeTariffIndex: Joi.string().length(6),
  withholdVatTariffIndex: Joi.string().length(6),
  regionalTaxIndex: Joi.string().length(3)
});

const locationSchema = Joi.object({
  countryCode: Joi.string().length(3).required(),
  state: Joi.string().min(0).max(3),
  cityUrl: Joi.string().min(0).max(13).required(),
  zip: Joi.string().min(0).max(11),
  street: Joi.string().min(0).max(40),
  serviceTelNo: Joi.string().min(0).max(16),
  countyIndex: Joi.string().length(3),
  municipalityIndex: Joi.string().length(5),
  telephone: Joi.string().min(0).max(15),
  regionOverrideFlag: Joi.boolean(),
  url: Joi.string().min(0).max(76)
});

const schemeDetailsSchema = Joi.object({
  crossBorderFeeCode: Joi.string().valid(...Object.values(OmnipayEnums.CrossBorderFeeCodeEnum)),
  masterCardIPQualificationCode: Joi.string().valid(...Object.values(OmnipayEnums.MasterCardIPQualificationCodeEnum)),
  masterCardIPValue: Joi.string().min(0).max(10),
  visaIPQualificationCode: Joi.string().valid(...Object.values(OmnipayEnums.VisaIPQualificationCodeEnum)),
  visaIPValue: Joi.string().min(0).max(10),
  paymentLowValue: Joi.boolean(),
  cbdipVisaSmiCode: Joi.string().length(9),
  mcPaymentFacilitatorId: Joi.string().min(0).max(11),
  mcIndependentSalesOrgId: Joi.string().min(0).max(11),
  visaCanIasfFee: Joi.boolean(),
  viLacFeeCode: Joi.string().valid(...Object.values(OmnipayEnums.ViLacFeeCodeEnum)),
  mcLacFeeCode: Joi.string().valid(...Object.values(OmnipayEnums.McLacFeeCodeEnum)),
  mcAbvFeeInd: Joi.boolean(),
  visaEuFeeIndCode: Joi.string().valid(...Object.values(OmnipayEnums.VisaEuFeeIndCodeEnum)),
  mcIntlEcomFee: Joi.boolean(),
  viUsApIafFee: Joi.string().length(3),
  pagobancomatIndex: Joi.string().length(3),
  visaPaymentFacilitatorId: Joi.string().length(8),
  globalTravelB2BFeeInd: Joi.string().length(3),
  mcCnpMotoFee: Joi.string().valid(...Object.values(OmnipayEnums.McCnpMotoFeeEnum)),
  mcPifFeeInd: Joi.boolean(),
  dinersIntesCode: Joi.string().min(0).max(4),
  amexModelIndex: Joi.string().length(3),
  merchantCountryOfOrigin: Joi.string().length(3),
  merchantSizeIndex: Joi.string().min(0).max(8),
  globalFees: Joi.array().items(globalFeeSchema)
});

const salesDetailsSchema = Joi.object({
  accSignedDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Signed date must be in the format YYYYMMDD and valid')
    .required(),
  accCreationDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Creation date must be in the format YYYYMMDD and valid')
    .required(),
  accApprovalDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Approval date must be in the format YYYYMMDD and valid')
    .required(),
  salesAgentId: Joi.string().length(8).required(),
  businessOwnerId: Joi.string().length(8).required(),
  salesChannelIndex: Joi.string().length(3).required(),
  salesCurrencyCode: Joi.string().length(3).required(),
  signedSalesValue: Joi.string()
    .pattern(/^\d{1,15}\.\d{2}$/)
    .custom(amountValidator, 'Signed sales value must be a valid amount')
    .min(0)
    .max(18),
  signedAverageTicketValue: Joi.string()
    .pattern(/^\d{1,15}\.\d{2}$/)
    .custom(amountValidator, 'Signed average ticket value must be a valid amount')
    .min(0)
    .max(18),
  salesLead: Joi.string().min(0).max(15),
  signedNumberSales: Joi.string().min(0).max(18),
  priorityMemo: Joi.string().min(0).max(100)
});

// Array item schemas
export const merchantAddressSchema = Joi.object({
  categoryIndex: Joi.string().length(3).required(),
  contactName: Joi.string().min(0).max(35),
  groupSpecific: Joi.boolean().required(),
  line1: Joi.string().min(0).max(35).required(),
  line2: Joi.string().min(0).max(35),
  line3: Joi.string().min(0).max(35),
  line4: Joi.string().min(0).max(35),
  line5: Joi.string().min(0).max(35),
  postCode: Joi.string().min(0).max(20),
  city: Joi.string().min(0).max(35).required(),
  state: Joi.string().min(0).max(3),
  countryCode: Joi.string().length(3).required(),
  telephone: Joi.string().min(0).max(15),
  fax: Joi.string().min(0).max(15),
  deliveryMethodIndex: Joi.string().length(3).required(),
  email: Joi.string().min(0).max(60),
  effectiveDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Effective date must be in the format YYYYMMDD and valid')
});

export const merchantAccountSchema = Joi.object({
  accountTypeIndex: Joi.string().length(3).required(),
  accountCurrencyCode: Joi.string().length(3).required(),
  billingLevel: Joi.boolean().required(),
  receiverCountryCode: Joi.string().length(3),
  statementTypeIndex: Joi.string().length(3),
  statementGenerationCode: Joi.string().length(3),
  annualFeeDueDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Annual fee due date must be in the format YYYYMMDD and valid'),
  calendarTypeIndex: Joi.string().length(3),
  paymentReference: Joi.string().min(0).max(18),
  payable: payableSchema,
  receivable: receivableSchema
});

export const serviceSchema = Joi.object({
  serviceIndex: Joi.string().length(3).required(),
  assigned: Joi.boolean().required(),
  tariffIndex: Joi.string().length(6).required(),
  floorLimit: Joi.string().min(0).max(18),
  dinersCode: Joi.string().valid(...Object.values(OmnipayEnums.DinersCodeEnum)),
  jcbStateCodeIndex: Joi.string().length(3),
  jcbAreaCode1Index: Joi.string().length(2),
  jcbArea2Index: Joi.string().length(2),
  merchantSubId: Joi.string().length(6),
  acquirerNoOfInstallment: Joi.number().min(0).max(99)
});

const referenceSchema = Joi.object({
  referenceIndex: Joi.string().length(3).required(),
  referenceValue: Joi.string().min(0).max(32)
});

export const merchantDeviceSchema = Joi.object({
  externalTerminalId: Joi.string().min(1).max(20).required(),
  serialNumber: Joi.string().min(0).max(9),
  contactName: Joi.string().min(0).max(35),
  terminalLocation: Joi.string().min(0).max(25),
  telephone: Joi.string().min(0).max(15),
  schemeId: Joi.string().min(0).max(6),
  accountCurrencyCode: Joi.string().length(3),
  systemBatchId: Joi.string().min(0).max(6),
  feeEffectiveDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Fee effective date must be in the format YYYYMMDD and valid')
    .required(),
  lastFeeDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Last fee date must be in the format YYYYMMDD and valid')
    .required(),
  clientFeeIndex: Joi.string().length(3),
  feeExpiryDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Fee expiry date must be in the format YYYYMMDD and valid')
    .required(),
  configOfTerminalIndex: Joi.string().length(3),
  installDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Install date must be in the format YYYYMMDD and valid')
    .required(),
  activeDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Active date must be in the format YYYYMMDD and valid')
    .required(),
  terminationDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Termination date must be in the format YYYYMMDD and valid')
    .required(),
  terminalPassword: Joi.string().min(0).max(8),
  terminalTypeIndex: Joi.string().length(3),
  terminalCountryCode: Joi.string().length(3),
  serviceProviderIndex: Joi.string().length(3),
  emv: Joi.boolean(),
  city: Joi.string().min(0).max(13),
  amex: Joi.boolean(),
  acquiringProfileIndex: Joi.string().length(3),
  templateId: Joi.string().min(0).max(4),
  amexPhoneNo1: Joi.string().min(0).max(24),
  amexPhoneNo2: Joi.string().min(0).max(24),
  relatedTId: Joi.string().min(0).max(20),
  waiver: Joi.string().min(0).max(30),
  socCode: Joi.string().min(0).max(15),
  setupFee: Joi.string().min(0).max(7),
  gprsRentalAmount: Joi.string().min(0).max(7),
  gprsRental: Joi.string().min(0).max(8),
  installed: Joi.string().min(0).max(12),
  workStation: Joi.string().min(0).max(50)
});

export const merchantAccountFeeSchema = Joi.object({
  recordIdNumber: Joi.string().length(10).required(),
  feePercentage: Joi.string()
    .min(0)
    .max(11)
    .pattern(/^\d{1,7}\.\d{1,4}$/)
    .custom(amountValidator, 'Fee percentage must be a valid amount')
    .optional(),
  feeBase: Joi.string()
    .min(0)
    .max(11)
    .pattern(/^\d{1,7}\.\d{1,4}$/)
    .custom(amountValidator, 'Fee base must be a valid amount')
    .optional(),
  feeMinimum: Joi.string()
    .min(0)
    .max(11)
    .pattern(/^\d{1,7}\.\d{1,4}$/)
    .custom(amountValidator, 'Fee minimum must be a valid amount')
    .optional(),
  feeMaximum: Joi.string()
    .min(0)
    .max(11)
    .pattern(/^\d{1,7}\.\d{1,4}$/)
    .custom(amountValidator, 'Fee maximum must be a valid amount')
    .optional(),
  feeMode: Joi.boolean().optional(),
  triggerHighValue: Joi.string().min(0).max(18).optional(),
  triggerVolumeHigh: Joi.string().min(0).max(18).optional(),
  effectiveDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Effective date must be in the format YYYYMMDD and valid')
    .optional()
});

export const transactionChargeSchema = Joi.object({
  recordIdNumber: Joi.string().length(10).required(),
  effectiveDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Effective date must be in the format YYYYMMDD and valid'),
  expiryDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Expiry date must be in the format YYYYMMDD and valid')
    .required(),
  feePercentage: Joi.string().min(0).max(11),
  feeBase: Joi.string().min(0).max(11),
  feeMinimum: Joi.string().min(0).max(11),
  feeMaximum: Joi.string().min(0).max(11),
  chargeCurrencyCode: Joi.string().length(3)
});

const merchantRequestSchema = Joi.object({
  requestIndex: Joi.string().length(3).required(),
  quantity: Joi.number().min(1).required()
});

export const paymentInstructionSchema = Joi.object({
  accountTypeIndex: Joi.string().length(3).required(),
  accountCurrencyCode: Joi.string().length(3).required(),
  instructionTypeIndex: Joi.string().length(3).required(),
  deductionStartDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Deduction start date must be in the format YYYYMMDD and valid')
    .required(),
  deductionEndDate: Joi.string()
    .length(8)
    .pattern(/^\d{8}$/)
    .custom(dateValidator, 'Deduction end date must be in the format YYYYMMDD and valid')
    .required(),
  deductionPercentage: Joi.string().min(0).max(11),
  deductionMinAmount: Joi.string().min(0).max(11),
  deductionMaxAmount: Joi.string().min(0).max(11),
  deductionBaseAmount: Joi.string().min(0).max(11),
  deductionTotalCnt: Joi.string().min(0).max(11),
  deductionTotalAmount: Joi.string().min(0).max(18),
  factorAgent: Joi.string().min(0).max(24),
  note: Joi.string().min(0).max(674)
});

const merchantPropertySchema = Joi.object({
  propertyIndex: Joi.string().length(3).required(),
  propertyValue: Joi.string().min(0).max(60)
});

// Main merchant detail schema
export const merchantDetailSchema = Joi.object({
  hierarchy: hierarchySchema.required(),
  requiredDetails: requiredDetailsSchema.required(),
  optionalDetails: optionalDetailsSchema,
  contract: contractSchema.required(),
  location: locationSchema.required(),
  schemeDetails: schemeDetailsSchema,
  salesDetails: salesDetailsSchema
});

// Main merchant onboarding schema
export const merchantOnboardingSchema = Joi.object({
  detail: merchantDetailSchema.required(),
  addresses: Joi.array().items(merchantAddressSchema).required(),
  accounts: Joi.array().items(merchantAccountSchema).required(),
  services: Joi.array().items(serviceSchema),
  references: Joi.array().items(referenceSchema),
  merchantDevices: Joi.array().items(merchantDeviceSchema),
  merchantAccountFees: Joi.array().items(merchantAccountFeeSchema),
  transactionCharges: Joi.array().items(transactionChargeSchema),
  merchantRequest: Joi.array().items(merchantRequestSchema),
  paymentInstructions: Joi.array().items(paymentInstructionSchema),
  merchantProperties: Joi.array().items(merchantPropertySchema)
});
