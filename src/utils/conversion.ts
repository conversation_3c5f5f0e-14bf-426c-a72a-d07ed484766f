import countries from 'i18n-iso-countries';
import { MerchantApplication, MerchantFileData, OmnipayMerchantDetails } from '../models/interfaces/requestDetails';
import { getCode } from 'country-list';
import { set } from 'mongoose';
export function alphaCodeToNumeric(code: string): string {
  try {
    if (code.length === 3) return countries.alpha3ToNumeric(code.toUpperCase()) ?? countries.alpha3ToNumeric('BGR')!;
    return countries.alpha2ToNumeric(code.toUpperCase()) ?? countries.alpha2ToNumeric('BG')!;
  } catch (error) {
    return countries.alpha2ToNumeric('BG')!;
  }
}

export function toAlphaTwoCode(code: string): string {
  try {
    if (code.length === 2) return code;
    if (code.length === 3 && code.match(/^[A-Z]{3}$/)) return countries.alpha3ToAlpha2(code.toUpperCase()) ?? 'BG';
    return countries.numericToAlpha2(code.toUpperCase()) ?? 'BG';
  } catch (error) {
    return 'BG';
  }
}

export function toAlphaThreeCode(code: string): string {
  try {
    if (code.length === 2) return countries.alpha2ToAlpha3(code.toUpperCase()) ?? 'BGR';
    if (code.length === 3 && code.match(/^[A-Z]{3}$/)) return code;
    return countries.numericToAlpha3(code.toUpperCase()) ?? 'BGR';
  } catch (error) {
    return 'BGR';
  }
}

export function convertApplicationToOmnipayDetails(application: MerchantApplication): OmnipayMerchantDetails {
  // Determine merchant type based on tradeOverInternet flag
  const merchantType = application.tradeOverInternet
    ? application.websites.length > 0
      ? 'Both'
      : 'Ecommerce'
    : 'Traditional';

  // Use trading name if available, otherwise use regular name
  const tradeName = application.tradeName || application.name;

  // Convert country code to alpha-2 format if needed
  const countryCode = toAlphaThreeCode(application.country);

  // Use trading address if available, otherwise use regular address
  const merchantStreet = application.address1TradingAs || application.address1;
  const merchantCity = application.cityTradingAs || application.city;
  const merchantZip = application.zipTradingAs || application.zip;

  // Default values for required fields that don't have direct mappings
  const DEFAULT_LANGUAGE = 'English';
  const DEFAULT_LEGAL_FORM = 'n/a'; //'Limited Company';
  const DEFAULT_RCC = 'Other Miscellaneous';
  const DEFAULT_SETTLEMENT_METHOD = 'Daily';

  return {
    midConfig: application.midConfiguration,
    // Required Details
    tradeName: tradeName,
    companyName: application.name,
    legalForm: DEFAULT_LEGAL_FORM,
    language: DEFAULT_LANGUAGE,
    merchantType: merchantType as 'Traditional' | 'Ecommerce' | 'Both',
    residentStatus: 'NA',
    rcc: DEFAULT_RCC,
    // Contract
    settlementMethod: DEFAULT_SETTLEMENT_METHOD as 'Daily' | 'Weekly' | 'Weekly (Thurs)' | 'Weekly (Tues)' | 'Monthly',
    clientRegion: 'n/a',
    // Location
    countryCode: countryCode,
    merchantStreet: merchantStreet,
    merchantZip: merchantZip,
    merchantCity: merchantCity,
    merchantUrls: application.websites.map((website) => {
      return {
        url: website.mccClassification.url,
        mcc: website.mccClassification.mcc
      };
    }),
    // Address
    address: application.address1,
    postCode: application.zip,
    telephone: application.phone[0].replace(/[^a-zA-Z0-9_\.\-\(\)\,]/g, ''), // Not available in MerchantApplication
    email: application.email || application.correspondenceEmail! || '',
    // Account
    accountCurrencies: application.additionalData.settlementCurrencies || [],
    accountNumber: application.additionalData?.bankSettlement?.at(0)?.bankAccountHolder || '',
    iban: application.additionalData?.bankSettlement?.at(0)?.bankIban, // Not available in MerchantApplication
    bic: application.additionalData?.bankSettlement?.at(0)?.bankCode,
    bankName: application.additionalData?.bankSettlement?.at(0)?.bankName,
    bankCity: '', // Not available in MerchantApplication
    // Optional Details
    registrationNumber: application.registrationNumber,
    vatRegistrationNumber: application.taxIdentificationNumber,
    mcOrgId: '' // Not available in MerchantApplication
  };
}

export function convertApplicationToAciDetails(application: MerchantApplication): MerchantFileData[] {
  const aciMerchantDetailsList: MerchantFileData[] = [];
  const isMasterMid = application.midConfiguration.isMasterMid;

  let terminalIndex = 0;

  // For each website, create a new entry for every currency
  for (const website of application.websites) {
    const settlementCurrencies = application.additionalData.settlementCurrencies?.filter((c) => c.length === 3) ?? [];

    for (const currency of settlementCurrencies) {
      const longTerminal = isMasterMid
        ? application.omnipayResult.childLongTerminals[terminalIndex].longTerminal
        : application.omnipayResult.longTerminal;

      const entity = {
        acquirer_country_code: 'BG',
        acquiring_institution_code: '000041',
        card_acceptor_country: 'BG',
        card_type: 'VISA,MASTERCARD',
        city: application.city,
        division_name: application.tradeName ?? application.name,
        long_terminal_id: longTerminal,
        mandatory_3ds: application.mandatory3ds ? 'YES' : 'NO',
        merchant_acceptor_name: application.tradeName ?? application.name,
        merchant_category_code: website.mccClassification.mcc,
        merchant_country_code: getCode(application.countryTradingAs ?? application.country) ?? '',
        merchant_currency: currency,
        merchant_name: application.tradeName ?? application.name,
        merchant_url: website.url.replace('https://', '').replace('http://', ''),
        additional_currencies: application.additionalData.currencies?.join(',') ?? '',
        payment_facilitator_name: '',
        send_dynamic_descriptor: website.statementDescriptor ? 'TRUE' : 'FALSE',
        street: application.address1TradingAs ?? application.address1,
        visa_acquirer_bin: '459304',
        visa_requestor_name: application.tradeName ?? application.name,
        mastercard_acquirer_bin: '536389',
        mastercard_requestor_name: `ACI Worldwide_${application.tradeName ?? application.name}`,
        descriptor_1: website.url.replace('https://', '').replace('http://', ''),
        descriptor_2: application.phone[0],
        onboarded: false,
        template: 'standard'
      } as MerchantFileData;
      aciMerchantDetailsList.push(entity);
      terminalIndex++;
    }
  }
  return aciMerchantDetailsList;
}

export function replaceAt(index: number, replacement: string, value: string): string {
  return value.substring(0, index) + replacement + value.substring(index + replacement.length);
}
