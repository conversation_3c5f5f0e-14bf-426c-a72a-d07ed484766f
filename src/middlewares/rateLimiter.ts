import rateLimit from 'express-rate-limit';
import { logger } from '../configs/logger';

/**
 * Default rate limiter configuration
 * Limits each IP to 100 requests per 5 minutes
 */
export const defaultRateLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: 'Too many requests from this IP, please try again after 5 minutes',
  handler: (req, res, next, options) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
    res.status(options.statusCode).json({
      status: 'error',
      message: options.message
    });
  }
});

/**
 * Stricter rate limiter for sensitive operations
 * Limits each IP to 10 requests per 2 minutes
 */
export const strictRateLimiter = rateLimit({
  windowMs: 2 * 60 * 1000, // 2 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many sensitive operations from this IP, please try again after 2 minutes',
  handler: (req, res, next, options) => {
    logger.warn(`Strict rate limit exceeded for IP: ${req.ip}`);
    res.status(options.statusCode).json({
      status: 'error',
      message: options.message
    });
  }
});

/**
 * API rate limiter for public API endpoints
 * Limits each IP to 200 requests per 5 minutes
 */
export const apiRateLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 200, // Limit each IP to 200 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many API requests from this IP, please try again after 5 minutes',
  handler: (req, res, next, options) => {
    logger.warn(`API rate limit exceeded for IP: ${req.ip}`);
    res.status(options.statusCode).json({
      status: 'error',
      message: options.message
    });
  }
});
