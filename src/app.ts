import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import appRouter from './routes/appRoutes';
import externalRouter from './routes/externalRoutes';
import errorHandler from './middlewares/errorHandler';
import { defaultRateLimiter } from './middlewares/rateLimiter';

const app = express();
const externalApp = express();

[app, externalApp].forEach((expressApp) => {
  expressApp.set('trust proxy', 1);
  expressApp.use(cors());
  expressApp.use(express.json());
  expressApp.use(cookieParser());

  // Apply rate limiting to all requests
  expressApp.use(defaultRateLimiter);

  expressApp.use(errorHandler);
});

app.use(appRouter);
externalApp.use(externalRouter);

export { app, externalApp };
