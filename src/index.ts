import dotenv from 'dotenv';
dotenv.config();
import { Express } from 'express';
import { app, externalApp } from './app';
import http from 'http';
import { logger } from './configs/logger';
import { connectDB } from './configs/mongoose';
import { startAciOnboardingCronJob, startCheckingScheduledUpdate, startOnboardingMessagesCron } from './services/cronService';
import { establishKafkaConnection, subscribeToTopics } from './services/kafkaService';

let server: http.Server | undefined;
let externalServer: http.Server | undefined;

connectDB(() => afterConnect(app));

app.on('ready', async () => {
  const port = process.env.PORT || 45590;
  const externalPort = process.env.EXTERNAL_PORT || 46001;
  server = app.listen(port, async () => {
    logger.info(`Server is listening on port ${port}`);
    if (process.env.START_ACI_ONBOARDING_CRON === 'true') await startAciOnboardingCronJob();
    if (process.env.START_SCHEDULED_UPDATES_CRON === 'true') await startCheckingScheduledUpdate();
    if (process.env.START_ONBOARDING_MESSAGES_CRON === 'true') await startOnboardingMessagesCron();
  });
  externalServer = externalApp.listen(externalPort, async () => {
    logger.info(`External server is listening on port ${externalPort}`);
  });
  const kafkaConnection = await establishKafkaConnection();
  if (kafkaConnection) subscribeToTopics();
});

process.on('SIGTERM', async () => {
  await closeServers('SIGTERM');
});

process.on('SIGINT', async () => {
  await closeServers('SIGINT');
});

async function closeServers(signal: string) {
  logger.info(`Received signal "${signal}", shutting down...`);
  if (server) await closeServer(server);
  logger.info('Exiting process...');
  process.exit(0);
}

async function closeServer(server: http.Server) {
  return new Promise<void>((resolve) => {
    server.close(() => {
      logger.info('The server has been stopped.');
      resolve();
    });
  });
}

/**
 * Function to be executed after successful connection
 *
 * @param app The Express app
 */
async function afterConnect(app: Express): Promise<void> {
  app.emit('ready');
}
