import express from 'express';
import { logger } from '../configs/logger';
import { multerConfigXlsx } from '../configs/multerConfig';
import multer from 'multer';
import { removeFileFromDirectory } from '../middlewares/fileManagement';
import { onboardToAci } from '../controllers/aciController';
import { verifyToken } from '../middlewares/token';
import {
  referenceData,
  serviceContracts,
  handleMaintenanceOperation,
  merchantReferences
} from '../controllers/omnipayController';
import { availabilityCheck } from '../middlewares/access';
import { strictRateLimiter, apiRateLimiter } from '../middlewares/rateLimiter';

const appRouter = express.Router();
const uploadXlsx = multer(multerConfigXlsx);

appRouter.get('', strictRateLimiter, function (_req: express.Request, res: express.Response) {
  logger.info(`Health-check probe/readiness: ${Date.now()}`);
  res.status(200).send('OK');
});

// Apply strict rate limiting to ACI onboarding
appRouter.post(
  '/bulk-upload',
  availabilityCheck,
  strictRateLimiter,
  verifyToken,
  removeFileFromDirectory,
  uploadXlsx.single('file'),
  onboardToAci
);

// Apply API rate limiting to reference data endpoints
appRouter.get('/omnipay/reference-data', availabilityCheck, apiRateLimiter, referenceData);

appRouter.get('/omnipay/merchant-references', availabilityCheck, apiRateLimiter, merchantReferences);

appRouter.get('/omnipay/service-contracts', availabilityCheck, apiRateLimiter, serviceContracts);

// Apply strict rate limiting to maintenance operations
appRouter
  .route('/omnipay/maintenance/:endpoint/:internalMerchantId')
  .get(availabilityCheck, strictRateLimiter, handleMaintenanceOperation)
  .put(availabilityCheck, strictRateLimiter, handleMaintenanceOperation)
  .post(availabilityCheck, strictRateLimiter, handleMaintenanceOperation)
  .delete(availabilityCheck, strictRateLimiter, handleMaintenanceOperation);

export default appRouter;
